import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { defaultMetadata, generateOrganizationStructuredData } from "@/lib/seo";
import DynamicFavicon from "@/components/molecules/DynamicFavicon";
import ErrorBoundary from "@/components/molecules/ErrorBoundary";

// Font configuration
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

// Enhanced SEO metadata
export const metadata: Metadata = defaultMetadata;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const organizationStructuredData = generateOrganizationStructuredData();

  return (
    <html lang="en" className="scroll-smooth">
      <head>
        {/* Default Favicon - will be replaced by DynamicFavicon */}
        <link rel="icon" type="image/png" href="/assets/favicons/Earthy Cocoa_.png" />
        <link rel="apple-touch-icon" href="/assets/favicons/Earthy Cocoa_.png" />

        {/* Organization Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationStructuredData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <DynamicFavicon />
        <ErrorBoundary>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
