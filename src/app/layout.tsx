import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

// Font configuration
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

// Metadata for SEO
export const metadata: Metadata = {
  title: {
    template: "%s | AfricSource",
    default: "AfricSource - Premium African Commodity Exports",
  },
  description: "Connecting global buyers with Africa's finest agricultural and mineral products. Trusted, transparent, and sustainable sourcing.",
  keywords: ["African exports", "commodities", "agricultural exports", "sustainable sourcing", "global trade"],
  authors: [{ name: "AfricSource" }],
  creator: "AfricSource",
  publisher: "AfricSource",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: "AfricSource - Premium African Commodity Exports",
    description: "Connecting global buyers with Africa's finest agricultural and mineral products. Trusted, transparent, and sustainable sourcing.",
    url: "https://africsource.com",
    siteName: "AfricSource",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AfricSource - Premium African Commodity Exports",
    description: "Connecting global buyers with Africa's finest agricultural and mineral products. Trusted, transparent, and sustainable sourcing.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
