import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { createServerClient, STORAGE_BUCKET_ID, ID } from "@/lib/appwrite";
import { InputFile } from "node-appwrite";

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await AppwriteServerAuth.protectRoute(request);

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    const validTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"];
    if (!validTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed." },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 5MB." },
        { status: 400 }
      );
    }

    try {
      // Log configuration for debugging
      console.log("Storage bucket ID:", STORAGE_BUCKET_ID);
      console.log("File details:", {
        name: file.name,
        size: file.size,
        type: file.type
      });

      // Upload the file to Appwrite Storage using server client
      const { storage } = createServerClient();

      console.log("Attempting to upload file to Appwrite storage...");

      // Convert File to Buffer for Appwrite InputFile
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      console.log("File converted to Buffer, size:", buffer.length);

      // Create InputFile from buffer (following the pattern from test-appwrite.js)
      const inputFile = InputFile.fromBuffer(buffer, file.name);

      const uploadedFile = await storage.createFile(
        STORAGE_BUCKET_ID,
        ID.unique(),
        inputFile
      );

      console.log("File uploaded successfully:", uploadedFile.$id);

      // Generate the file URL manually using the Appwrite endpoint
      const fileUrl = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${STORAGE_BUCKET_ID}/files/${uploadedFile.$id}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;

      console.log("Generated file URL:", fileUrl);

      return NextResponse.json({
        url: fileUrl,
        fileId: uploadedFile.$id,
        fileName: file.name,
        size: file.size,
        type: file.type
      }, { status: 201 });
    } catch (error: any) {
      console.error("Detailed error saving file:", {
        message: error.message,
        code: error.code,
        type: error.type,
        response: error.response,
        stack: error.stack
      });

      // Return more specific error message
      const errorMessage = error.message || "Failed to save file";
      return NextResponse.json(
        {
          error: errorMessage,
          details: error.code ? `Error code: ${error.code}` : undefined
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}
