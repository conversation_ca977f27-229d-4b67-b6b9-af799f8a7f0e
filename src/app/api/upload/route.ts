import { NextRequest, NextResponse } from "next/server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { AppwriteStorageService } from "@/lib/appwrite-storage";

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await AppwriteServerAuth.protectRoute(request);

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    const validTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"];
    if (!validTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed." },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 5MB." },
        { status: 400 }
      );
    }

    try {
      // Upload the file to Appwrite Storage
      const uploadedFile = await AppwriteStorageService.uploadFile(file);

      // Get the file URL
      const fileUrl = AppwriteStorageService.getFileUrl(uploadedFile.$id);

      return NextResponse.json({
        url: fileUrl,
        fileId: uploadedFile.$id,
        fileName: file.name,
        size: file.size,
        type: file.type
      }, { status: 201 });
    } catch (error) {
      console.error("Error saving file:", error);
      return NextResponse.json(
        { error: "Failed to save file" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}
