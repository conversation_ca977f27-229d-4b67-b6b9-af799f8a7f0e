import { NextRequest, NextResponse } from "next/server";
import { AppwriteContactService } from "@/lib/appwrite-server";
import { ContactSubmissionRequest, ContactSubmissionResponse, ApiErrorResponse } from "@/types/types";

export async function POST(request: NextRequest): Promise<NextResponse<ContactSubmissionResponse | ApiErrorResponse>> {
  try {
    const body: ContactSubmissionRequest = await request.json();
    
    // Validate required fields
    if (!body.name || !body.email || !body.message) {
      return NextResponse.json(
        { error: "Name, email, and message are required" },
        { status: 400 }
      );
    }
    
    // Create contact submission using Appwrite
    const contactSubmission = await AppwriteContactService.createContactSubmission({
      name: body.name,
      email: body.email,
      phone: body.phone,
      company: body.company,
      subject: body.subject,
      message: body.message,
    });

    return NextResponse.json(
      { success: true, id: contactSubmission.$id },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error submitting contact form:", error);
    return NextResponse.json(
      { error: "Failed to submit contact form" },
      { status: 500 }
    );
  }
}
