import { NextRequest, NextResponse } from "next/server";
import { AppwriteAuthService } from "@/lib/appwrite-auth";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Attempt login
    const user = await AppwriteAuthService.login({ email, password });

    // Create session token
    const sessionToken = await AppwriteAuthService.createSession(user);

    // Create response
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.$id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });

    // Set secure HTTP-only cookie
    const cookieStore = await cookies();
    cookieStore.set('appwrite-session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/'
    });

    return response;
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Invalid email or password" },
      { status: 401 }
    );
  }
}
