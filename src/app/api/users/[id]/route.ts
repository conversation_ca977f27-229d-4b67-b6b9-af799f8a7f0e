import { NextRequest, NextResponse } from "next/server";
import { AppwriteUserService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";
import { hash } from 'bcrypt';

// GET a specific user (admin only or self)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const currentUser = await AppwriteServerAuth.protectRoute(request);

    // Allow admin or self to view user details
    if (currentUser.role !== "ADMIN" && currentUser.$id !== params.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = await AppwriteUserService.getUserById(params.id);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Return user without password
    const safeUser = {
      $id: user.$id,
      name: user.name,
      email: user.email,
      role: user.role,
      $createdAt: user.$createdAt,
      $updatedAt: user.$updatedAt
    };

    return NextResponse.json(safeUser);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 }
    );
  }
}

// PUT update a user (admin only, or self)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const currentUser = await AppwriteServerAuth.protectRoute(request);

    // Allow admin or self to update user details
    if (currentUser.role !== "ADMIN" && currentUser.$id !== params.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json(
        { error: "Name and email are required" },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await AppwriteUserService.getUserById(params.id);

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if email is already taken by another user
    if (body.email !== existingUser.email) {
      const allUsers = await AppwriteUserService.getAllUsers({
        search: body.email,
        limit: 10
      });

      const emailExists = allUsers.find(user =>
        user.email.toLowerCase() === body.email.toLowerCase() && user.$id !== params.id
      );

      if (emailExists) {
        return NextResponse.json(
          { error: "Email already in use" },
          { status: 409 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      name: body.name,
      email: body.email
    };

    // Only admin can change roles
    if (currentUser.role === "ADMIN" && body.role) {
      updateData.role = body.role;
    }

    // Update password if provided
    if (body.password) {
      updateData.password = await hash(body.password, 12);
    }

    // Update user
    const user = await AppwriteUserService.updateUser(params.id, updateData);

    // Return user without password
    const safeUser = {
      $id: user.$id,
      name: user.name,
      email: user.email,
      role: user.role,
      $createdAt: user.$createdAt,
      $updatedAt: user.$updatedAt
    };

    return NextResponse.json(safeUser);
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    );
  }
}

// DELETE a user (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate and authorize admin user
    const currentUser = await AppwriteServerAuth.protectAdminRoute(request);

    // Check if user exists
    const existingUser = await AppwriteUserService.getUserById(params.id);

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Prevent deleting yourself
    if (currentUser.$id === params.id) {
      return NextResponse.json(
        { error: "You cannot delete your own account" },
        { status: 400 }
      );
    }

    // Delete user (this will also handle related products and other user data)
    const success = await AppwriteUserService.deleteUser(params.id);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to delete user" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    );
  }
}
