import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

// Helper function to create a slug from a string
function createSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
}

// GET a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const product = await prisma.product.findUnique({
      where: {
        id: params.id
      },
      include: {
        images: true,
        categories: true,
        tags: true,
        specifications: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(product);
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { error: "Failed to fetch product" },
      { status: 500 }
    );
  }
}

// PUT update a product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.description) {
      return NextResponse.json(
        { error: "Name and description are required" },
        { status: 400 }
      );
    }
    
    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: {
        id: params.id
      },
      include: {
        images: true,
        categories: true,
        tags: true,
        specifications: true
      }
    });
    
    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }
    
    // Create slug from name
    const slug = createSlug(body.name);
    
    // Check if slug already exists for another product
    if (body.name !== existingProduct.name) {
      const slugExists = await prisma.product.findUnique({
        where: {
          slug
        }
      });
      
      if (slugExists && slugExists.id !== params.id) {
        return NextResponse.json(
          { error: "A product with this name already exists" },
          { status: 409 }
        );
      }
    }
    
    // Update product with transaction to handle related data
    const product = await prisma.$transaction(async (tx) => {
      // Update the product
      const updatedProduct = await tx.product.update({
        where: {
          id: params.id
        },
        data: {
          name: body.name,
          slug,
          description: body.description,
          details: body.details || null,
          price: body.price || null,
          stock: body.stock || "Available"
        }
      });
      
      // Update specifications
      // First, delete existing specifications
      await tx.specification.deleteMany({
        where: {
          productId: params.id
        }
      });
      
      // Then, add new specifications
      if (body.specifications && Array.isArray(body.specifications)) {
        for (const spec of body.specifications) {
          if (spec.key && spec.value) {
            await tx.specification.create({
              data: {
                key: spec.key,
                value: spec.value,
                product: {
                  connect: {
                    id: updatedProduct.id
                  }
                }
              }
            });
          }
        }
      }
      
      // Update categories
      // First, disconnect all categories
      await tx.product.update({
        where: {
          id: params.id
        },
        data: {
          categories: {
            set: []
          }
        }
      });
      
      // Then, connect new categories
      if (body.categories && Array.isArray(body.categories)) {
        for (const categoryId of body.categories) {
          await tx.product.update({
            where: {
              id: updatedProduct.id
            },
            data: {
              categories: {
                connect: {
                  id: categoryId
                }
              }
            }
          });
        }
      }
      
      // Update tags
      // First, disconnect all tags
      await tx.product.update({
        where: {
          id: params.id
        },
        data: {
          tags: {
            set: []
          }
        }
      });
      
      // Then, connect new tags
      if (body.tags && Array.isArray(body.tags)) {
        for (const tagId of body.tags) {
          await tx.product.update({
            where: {
              id: updatedProduct.id
            },
            data: {
              tags: {
                connect: {
                  id: tagId
                }
              }
            }
          });
        }
      }
      
      // Handle images
      if (body.images && Array.isArray(body.images)) {
        // Get existing image IDs
        const existingImageIds = existingProduct.images.map(img => img.id);
        
        // Get new image IDs from request
        const newImageIds = body.images
          .filter((img: { id}) => img.id)
          .map((img: { id }) => img.id);
        
        // Find images to delete (in existing but not in new)
        const imagesToDelete = existingImageIds.filter(id => !newImageIds.includes(id));
        
        // Delete images that are no longer needed
        if (imagesToDelete.length > 0) {
          await tx.productImage.deleteMany({
            where: {
              id: {
                in: imagesToDelete
              }
            }
          });
        }
        
        // Update or create images
        for (const image of body.images) {
          if (image.id) {
            // Update existing image
            await tx.productImage.update({
              where: {
                id: image.id
              },
              data: {
                url: image.url,
                alt: image.alt || updatedProduct.name,
                isFeatured: image.isFeatured || false
              }
            });
          } else if (image.url) {
            // Create new image
            await tx.productImage.create({
              data: {
                url: image.url,
                alt: image.alt || updatedProduct.name,
                isFeatured: image.isFeatured || false,
                product: {
                  connect: {
                    id: updatedProduct.id
                  }
                }
              }
            });
          }
        }
      }
      
      return updatedProduct;
    });
    
    // Fetch the complete updated product with relations
    const completeProduct = await prisma.product.findUnique({
      where: {
        id: product.id
      },
      include: {
        images: true,
        categories: true,
        tags: true,
        specifications: true
      }
    });
    
    return NextResponse.json(completeProduct);
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { error: "Failed to update product" },
      { status: 500 }
    );
  }
}

// DELETE a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: {
        id: params.id
      }
    });
    
    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }
    
    // Delete product (cascade will handle related entities)
    await prisma.product.delete({
      where: {
        id: params.id
      }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { error: "Failed to delete product" },
      { status: 500 }
    );
  }
}
