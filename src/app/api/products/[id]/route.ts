import { NextRequest, NextResponse } from "next/server";
import { AppwriteProductService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

// Helper function to create slug from name
function createSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to transform specifications array to object
function transformSpecifications(product: any): any {
  if (!product || !product.specifications) return product;

  const specsObject = product.specifications.reduce((acc: any, spec: any) => {
    acc[spec.key] = spec.value;
    return acc;
  }, {});

  return {
    ...product,
    specifications: specsObject
  };
}



// GET a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const product = await AppwriteProductService.getProductById(id);

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Transform specifications array to object for frontend compatibility
    const transformedProduct = transformSpecifications(product);

    return NextResponse.json(transformedProduct);
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { error: "Failed to fetch product" },
      { status: 500 }
    );
  }
}

// PUT update a product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Authenticate user
    const user = await AppwriteServerAuth.protectRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.description) {
      return NextResponse.json(
        { error: "Name and description are required" },
        { status: 400 }
      );
    }

    // Check if product exists
    const existingProduct = await AppwriteProductService.getProductById(id);

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Create slug from name
    const slug = createSlug(body.name);

    // Check if slug already exists for another product
    if (body.name !== existingProduct.name) {
      const allProducts = await AppwriteProductService.getAllProducts({
        search: body.name,
        limit: 10
      });

      const slugExists = allProducts.find(p => p.slug === slug && p.$id !== id);

      if (slugExists) {
        return NextResponse.json(
          { error: "A product with this name already exists" },
          { status: 409 }
        );
      }
    }
    
    // Update product using Appwrite
    const updatedProduct = await AppwriteProductService.updateProduct(id, {
      name: body.name,
      description: body.description,
      details: body.details,
      price: body.price,
      stock: body.stock || "Available",
      categoryIds: body.categories,
      tagIds: body.tags,
      images: body.images,
      specifications: body.specifications
    });

    // Fetch the complete product with relations
    const completeProduct = await AppwriteProductService.getProductById(updatedProduct.$id);

    return NextResponse.json(completeProduct);
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { error: "Failed to update product" },
      { status: 500 }
    );
  }
}

// DELETE a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Authenticate user
    const user = await AppwriteServerAuth.protectRoute(request);

    // Check if product exists
    const existingProduct = await AppwriteProductService.getProductById(id);

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }
    
    // Delete product (this will handle related data)
    const success = await AppwriteProductService.deleteProduct(id);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to delete product" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { error: "Failed to delete product" },
      { status: 500 }
    );
  }
}
