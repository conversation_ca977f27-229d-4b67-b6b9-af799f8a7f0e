import { NextRequest, NextResponse } from "next/server";
import { AppwriteProductService, AppwriteCategoryService, AppwriteTagService } from "@/lib/appwrite-server";
import { AppwriteServerAuth } from "@/lib/appwrite-auth";

// Helper function to create a slug from a string
function createSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
}

// Helper function to transform specifications array to object
function transformSpecifications(product: any): any {
  if (!product || !product.specifications) return product;

  const specsObject = product.specifications.reduce((acc: any, spec: any) => {
    acc[spec.key] = spec.value;
    return acc;
  }, {});

  return {
    ...product,
    specifications: specsObject
  };
}


// GET all products
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query options for Appwrite (without search to avoid fulltext index requirement)
    const queryOptions = {
      limit: search ? 1000 : limit, // Get more products if we need to search client-side
      offset: search ? 0 : offset,
      orderBy: '$createdAt',
      orderDirection: 'desc' as const,
      filters: {}
    };

    // Get products from Appwrite
    let products = await AppwriteProductService.getAllProducts(queryOptions);

    // Apply client-side search filtering if search term is provided
    if (search) {
      const searchTerm = search.toLowerCase();
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        (product.details && product.details.toLowerCase().includes(searchTerm))
      );

      // Apply pagination after filtering
      products = products.slice(offset, offset + limit);
    }

    // Get products with full relations
    const productsWithRelations = await Promise.all(
      products.map(async (product) => {
        return await AppwriteProductService.getProductById(product.$id);
      })
    );

    // Filter by category and tag if specified
    let filteredProducts = productsWithRelations.filter(product => product !== null);

    if (category) {
      filteredProducts = filteredProducts.filter(product =>
        product?.categories.some(cat => cat.name === category)
      );
    }

    if (tag) {
      filteredProducts = filteredProducts.filter(product =>
        product?.tags.some(t => t.name === tag)
      );
    }

    // Transform specifications array into object
    const transformedProducts = filteredProducts.map(transformSpecifications);

    return NextResponse.json(transformedProducts);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}


// POST create a new product
export async function POST(request: NextRequest) {
  try {
    // Authenticate user using Appwrite
    const user = await AppwriteServerAuth.protectRoute(request);

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.description) {
      return NextResponse.json(
        { error: "Name and description are required" },
        { status: 400 }
      );
    }

    // Create slug from name
    const slug = createSlug(body.name);

    // Check if slug already exists by searching products
    const existingProducts = await AppwriteProductService.getAllProducts({
      search: body.name,
      limit: 10
    });

    const existingProduct = existingProducts.find(p => p.slug === slug);
    if (existingProduct) {
      return NextResponse.json(
        { error: "A product with this name already exists" },
        { status: 409 }
      );
    }

    // Create product using Appwrite
    const newProduct = await AppwriteProductService.createProduct({
      name: body.name,
      description: body.description,
      details: body.details,
      price: body.price,
      stock: body.stock || "Available",
      categoryIds: body.categories,
      tagIds: body.tags,
      specifications: body.specifications
    }, user.$id);

    // Fetch the complete product with relations
    const completeProduct = await AppwriteProductService.getProductById(newProduct.$id);

    if (!completeProduct) {
      return NextResponse.json(
        { error: "Failed to retrieve created product" },
        { status: 500 }
      );
    }

    // Transform specifications array to object before returning
    const transformedProduct = transformSpecifications(completeProduct);

    return NextResponse.json(transformedProduct, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);
    return NextResponse.json(
      { error: "Failed to create product" },
      { status: 500 }
    );
  }
}