import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

// Helper function to create a slug from a string
function createSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
}

// Helper function to transform specifications array to object
function transformSpecifications(product) {
  const specsObject = product.specifications.reduce((acc, spec ) => {
    acc[spec.key] = spec.value;
    return acc;
  }, {});
  return {
    ...product,
    specifications: specsObject
  };
}


// GET all products
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const search = searchParams.get('search');

    // Build the where clause based on query parameters
   const where = {};

if (category) {
  where.categories = {
    some: {
      name: category,
    },
  };
}


    if (tag) {
      where.tags = {
        some: {
          name: tag
        }
      };
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        images: true,
        categories: true,
        tags: true,
        specifications: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Transform specifications array into object
    const transformedProducts = products.map(transformSpecifications);

    return NextResponse.json(transformedProducts);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}


// POST create a new product
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.description) {
      return NextResponse.json(
        { error: "Name and description are required" },
        { status: 400 }
      );
    }

    // Create slug from name
    const slug = createSlug(body.name);

    // Check if slug already exists
    const existingProduct = await prisma.product.findUnique({
      where: {
        slug
      }
    });

    if (existingProduct) {
      return NextResponse.json(
        { error: "A product with this name already exists" },
        { status: 409 }
      );
    }

    // Create product with transaction to handle related data
    const product = await prisma.$transaction(async (tx) => {
      // Create the product
      const newProduct = await tx.product.create({
        data: {
          name: body.name,
          slug,
          description: body.description,
          details: body.details || null,
          price: body.price || null,
          stock: body.stock || "Available",
          createdBy: {
            connect: {
              id: user.id
            }
          }
        }
      });

      // Add specifications if provided
      if (Array.isArray(body.specifications)) {
        for (const spec of body.specifications) {
          if (spec.key && spec.value !== undefined) {
            await tx.specification.create({
              data: {
                key: spec.key,
                value: spec.value, // this can now be string, array, object, etc.
                product: {
                  connect: { id: newProduct.id },
                },
              },
            });
          }
        }
      }




      // Add categories if provided
      if (body.categories && Array.isArray(body.categories)) {
        for (const categoryId of body.categories) {
          await tx.product.update({
            where: {
              id: newProduct.id
            },
            data: {
              categories: {
                connect: {
                  id: categoryId
                }
              }
            }
          });
        }
      }

      // Add tags if provided
      if (body.tags && Array.isArray(body.tags)) {
        for (const tagId of body.tags) {
          await tx.product.update({
            where: {
              id: newProduct.id
            },
            data: {
              tags: {
                connect: {
                  id: tagId
                }
              }
            }
          });
        }
      }

      // Add images if provided
      if (body.images && Array.isArray(body.images)) {
        for (const image of body.images) {
          if (image.url) {
            await tx.productImage.create({
              data: {
                url: image.url,
                alt: image.alt || body.name,
                isFeatured: image.isFeatured || false,
                product: {
                  connect: {
                    id: newProduct.id
                  }
                }
              }
            });
          }
        }
      }

      return newProduct;
    });

    // Fetch the complete product with relations
    const completeProduct = await prisma.product.findUnique({
      where: {
        id: product.id
      },
      include: {
        images: true,
        categories: true,
        tags: true,
        specifications: true
      }
    });

    // Transform specifications array to object before returning
    const transformedProduct = transformSpecifications(completeProduct);

    return NextResponse.json(transformedProduct, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);
    return NextResponse.json(
      { error: "Failed to create product" },
      { status: 500 }
    );
  }
}