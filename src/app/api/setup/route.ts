import { NextRequest, NextResponse } from "next/server";
import { createServer<PERSON>lient, DATABASE_ID } from "@/lib/appwrite";

// Test endpoint to verify database collections exist
export async function GET(request: NextRequest) {
  try {
    const { databases } = createServerClient();
    
    const collections = [
      'users',
      'products', 
      'categories',
      'tags',
      'product_categories',
      'product_tags',
      'product_images',
      'specifications',
      'contact_submissions'
    ];
    
    const results = {};
    
    for (const collectionId of collections) {
      try {
        const documents = await databases.listDocuments(DATABASE_ID, collectionId, []);
        results[collectionId] = {
          exists: true,
          count: documents.total,
          status: 'OK'
        };
      } catch (error: any) {
        results[collectionId] = {
          exists: false,
          error: error.message,
          status: 'ERROR'
        };
      }
    }
    
    return NextResponse.json({
      database_id: DATABASE_ID,
      collections: results,
      timestamp: new Date().toISOString()
    }, { status: 200 });
    
  } catch (error: any) {
    console.error("Database setup check error:", error);
    return NextResponse.json(
      { error: "Failed to check database setup" },
      { status: 500 }
    );
  }
}

// Endpoint to run database setup (admin only)
export async function POST(request: NextRequest) {
  try {
    // This would typically require admin authentication
    // For now, we'll just return instructions
    
    return NextResponse.json({
      message: "Database setup instructions",
      steps: [
        "1. Create all required collections in Appwrite Console",
        "2. Set up proper permissions for each collection",
        "3. Run: node scripts/setup-database.js",
        "4. Verify setup with GET /api/setup"
      ],
      collections_needed: [
        "users",
        "products", 
        "categories",
        "tags",
        "product_categories",
        "product_tags",
        "product_images",
        "specifications",
        "contact_submissions"
      ],
      schema_reference: "/docs/database-schema.md"
    }, { status: 200 });
    
  } catch (error: any) {
    console.error("Database setup error:", error);
    return NextResponse.json(
      { error: "Failed to setup database" },
      { status: 500 }
    );
  }
}
