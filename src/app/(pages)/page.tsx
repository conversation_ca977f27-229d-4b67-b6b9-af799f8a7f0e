"use client";

import React from "react";
import HeroSection from "@/components/home/<USER>";
import ProductRangeSection from "@/components/home/<USER>";
import {
  aboutSectionData,
  partnershipSectionData,
  careersSectionData,
} from "@/store";
import CompanyHighlights from "@/components/home/<USER>";
import WhySection from "@/components/home/<USER>";
import PartnershipSection from "@/components/home/<USER>";
import CareersSection from "@/components/home/<USER>";
import AboutSection from "@/components/home/<USER>";

// ... all your individual sections (WhySection, CompanyHighlights, etc.) stay unchanged ...
// Keep the components you defined: WhySection, CompanyHighlights, PartnershipSection, CareersSection, AboutSection

// Only this part changes:

const HomePage = () => {
  const aboutData = aboutSectionData;
  const partnershipData = partnershipSectionData;
  const careersData = careersSectionData;

  return (
    <>
      <HeroSection />
      <CompanyHighlights />
      <WhySection data={aboutData} />
      <ProductRangeSection />
      <AboutSection />
      <PartnershipSection data={partnershipData} />
      <CareersSection data={careersData} />
    </>
  );
};

export default HomePage;
