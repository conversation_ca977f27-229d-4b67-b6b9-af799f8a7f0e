import { Metadata } from 'next';
import { generateProductMetadata } from '@/lib/seo';

// This would typically fetch product data for metadata generation
async function getProduct(productId: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/products/${productId}`, {
      next: { revalidate: 3600 } // Revalidate every hour
    });
    
    if (!response.ok) {
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching product for metadata:', error);
    return null;
  }
}

export async function generateMetadata({ params }: { params: Promise<{ productId: string }> }): Promise<Metadata> {
  const { productId } = await params;
  const product = await getProduct(productId);
  
  if (!product) {
    return {
      title: 'Product Not Found | AfricSource',
      description: 'The requested product could not be found.',
      robots: {
        index: false,
        follow: false,
      },
    };
  }
  
  return generateProductMetadata({
    name: product.name,
    description: product.description,
    images: product.images,
    price: product.price,
    categories: product.categories,
    tags: product.tags
  });
}
