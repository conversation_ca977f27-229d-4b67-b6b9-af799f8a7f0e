"use client";

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Package, Globe, Clock, Award, CheckCircle, Star } from 'lucide-react';
import axios from 'axios';
import Image from "next/image";
import AOS from 'aos';

function ProductPage({ params }: { params: Promise<{ productId: string }> }) {
  const actualParams = React.use(params);
  const productId = actualParams.productId;
  const [products, setProducts] = useState<any[]>([]);
  const [currentProductId, setCurrentProductId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        const res = await axios.get("/api/products");
        setProducts(res.data);
        setCurrentProductId(productId);
      } catch (error) {
        console.error("Error fetching products", error);
        setError("Failed to load products");
      } finally {
        setLoading(false);
      }
    };
    fetchProducts();
  }, [productId]);

  // Refresh AOS when product changes
  useEffect(() => {
    AOS.refresh();
  }, [currentProductId]);

  const [selectedTab, setSelectedTab] = useState('overview');
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const filtered = products.filter(p => p.$id === currentProductId);
  const currentProduct = filtered.length > 0 ? filtered[0] : null;

  const currentIndex = products.findIndex(p => p.$id === currentProductId);

  const nextProduct = () => {
    const nextIndex = (currentIndex + 1) % products.length;
    setCurrentProductId(products[nextIndex].$id);
    setSelectedTab('overview');
    setSelectedImageIndex(0); // Reset image selection
  };

  const prevProduct = () => {
   const prevIndex = (currentIndex - 1 + products.length) % products.length;
    setCurrentProductId(products[prevIndex].$id);
    setSelectedTab('overview');
    setSelectedImageIndex(0); // Reset image selection
  };

  // Helper functions for image management
  const getMainImage = (product: any) => {
    if (!product?.images || product.images.length === 0) return null;

    // Look for main image first (when enhanced schema is available)
    const mainImage = product.images.find((img: any) => img.isMain);
    if (mainImage) return mainImage;

    // Fall back to featured image
    const featuredImage = product.images.find((img: any) => img.isFeatured);
    if (featuredImage) return featuredImage;

    // Fall back to first image
    return product.images[0];
  };

  const getSortedImages = (product: any) => {
    if (!product?.images || product.images.length === 0) return [];

    // Sort by order field if available, otherwise by creation date
    return [...product.images].sort((a: any, b: any) => {
      if (a.order !== undefined && b.order !== undefined) {
        return a.order - b.order;
      }
      return new Date(a.$createdAt).getTime() - new Date(b.$createdAt).getTime();
    });
  };

  const getCurrentImage = (product: any) => {
    const sortedImages = getSortedImages(product);
    return sortedImages[selectedImageIndex] || getMainImage(product);
  };

  const specificationIcons = {
    origin: Globe,
    quality: Star,
    packaging: Package,
    moq: Package,
    leadTime: Clock,
    certifications: Award
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-amber-400 pt-18">
        <div className="text-2xl text-emerald-800 font-bold" data-aos="fade-in">
          Loading product...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-amber-400 pt-18">
        <div className="text-2xl text-emerald-800 font-bold" data-aos="fade-in">
          {error}
        </div>
      </div>
    );
  }

  if (!currentProduct) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-amber-400 pt-18">
        <div className="text-2xl text-emerald-800 font-bold" data-aos="fade-in">
          Product not found.
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-25 overflow-hidden">
      {/* Product Header with Side Navigation */}
      <div className="bg-gradient-to-r from-emerald-800 to-emerald-700 text-white relative">
        {/* Left Navigation Button */}
        <button 
          onClick={prevProduct}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-all duration-200 hover:scale-110 shadow-lg"
          data-aos="fade-right"
          data-aos-delay="100"
        >
          <ChevronLeft size={24} />
        </button>

        {/* Right Navigation Button */}
        <button 
          onClick={nextProduct}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-all duration-200 hover:scale-110 shadow-lg"
          data-aos="fade-left"
          data-aos-delay="100"
        >
          <ChevronRight size={24} />
        </button>

        {/* Product Counter */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div 
            className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full"
            data-aos="fade-down"
            data-aos-delay="200"
          >
            <span className="text-white font-medium text-sm">
              {currentIndex + 1} of {products.length}
            </span>
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-6 py-12">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div data-aos="fade-right" data-aos-delay="300">
              <div
                className="inline-block px-3 py-1 bg-yellow-400 text-emerald-800 rounded-full text-sm font-medium mb-4"
                data-aos="zoom-in"
                data-aos-delay="400"
              >
                {currentProduct?.categories?.[0]?.name || 'Product'}
              </div>
              <h1 
                className="text-4xl font-bold mb-4"
                data-aos="fade-up"
                data-aos-delay="500"
              >
                {currentProduct?.name}
              </h1>
              <p 
                className="text-xl text-emerald-100 mb-6"
                data-aos="fade-up"
                data-aos-delay="600"
              >
                {currentProduct?.description}
              </p>
              <div 
                className="flex gap-4"
                data-aos="fade-up"
                data-aos-delay="700"
              >
                <button className="px-6 py-3 bg-yellow-400 text-emerald-800 rounded-lg font-semibold hover:bg-yellow-300 transition-colors duration-200">
                 About
                </button>
                <button className="px-6 py-3 border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-emerald-800 transition-colors duration-200">
                  Contact Supplier
                </button>
              </div>
            </div>
            <div 
              className="relative"
              data-aos="fade-left"
              data-aos-delay="400"
            >
              <div
                className="bg-white p-6 rounded-xl shadow-2xl"
                data-aos="zoom-in"
                data-aos-delay="600"
              >
                {/* Main Image Display */}
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4 relative">
                  <div className="w-full h-full bg-gradient-to-br from-emerald-50 to-emerald-100 flex items-center justify-center">
                    {getCurrentImage(currentProduct)?.url ? (
                      <Image
                        src={getCurrentImage(currentProduct).url}
                        alt={getCurrentImage(currentProduct).alt || currentProduct?.name || 'Product image'}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                      />
                    ) : (
                      <div className="text-gray-400 text-center">
                        <Package className="h-16 w-16 mx-auto mb-2" />
                        <p>No image available</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Image Thumbnails */}
                {getSortedImages(currentProduct).length > 1 && (
                  <div className="flex space-x-2 overflow-x-auto pb-2">
                    {getSortedImages(currentProduct).map((image: any, index: number) => (
                      <button
                        key={image.$id || index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                          selectedImageIndex === index
                            ? 'border-emerald-500 ring-2 ring-emerald-200'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Image
                          src={image.url}
                          alt={image.alt || `Product image ${index + 1}`}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Tabs */}
      <div className="mx-auto px-15 py-8 bg-white">
        <div 
          className="border-b border-gray-200 mb-8"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <nav className="flex space-x-8">
            {['overview', 'specifications', 'certifications'].map((tab, index) => (
              <button
                key={tab}
                onClick={() => setSelectedTab(tab)}
                className={`py-4 px-2 border-b-2 font-medium text-sm capitalize transition-colors duration-200 ${
                  selectedTab === tab
                    ? 'border-emerald-800 text-emerald-800'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                data-aos="fade-up"
                data-aos-delay={200 + (index * 100)}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="grid md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            {selectedTab === 'overview' && (
              <div className="space-y-6">
                <div data-aos="fade-up" data-aos-delay="200">
                  <h2 className="text-2xl font-bold text-emerald-800 mb-4">Product Details</h2>
                  <p className="text-gray-700 leading-relaxed text-lg">{currentProduct.details}</p>
                </div>
                
                <div className="grid sm:grid-cols-2 gap-6">
                  <div 
                    className="bg-emerald-50 p-6 rounded-xl"
                    data-aos="fade-right"
                    data-aos-delay="300"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <Globe className="text-emerald-600" size={24} />
                      <h3 className="font-semibold text-emerald-800">Origin</h3>
                    </div>
                    <p className="text-gray-700">{currentProduct?.specifications?.origin || 'Not specified'}</p>
                  </div>
                  
                  <div 
                    className="bg-yellow-50 p-6 rounded-xl"
                    data-aos="fade-left"
                    data-aos-delay="400"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <Clock className="text-yellow-600" size={24} />
                      <h3 className="font-semibold text-emerald-800">Lead Time</h3>
                    </div>
                    <p className="text-gray-700">{currentProduct?.specifications?.leadTime || 'Contact for details'}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedTab === 'specifications' && (
              <div className="space-y-6">
                <h2 
                  className="text-2xl font-bold text-emerald-800 mb-6"
                  data-aos="fade-up"
                  data-aos-delay="100"
                >
                  Technical Specifications
                </h2>
                <div className="space-y-4">
                  {currentProduct.specifications && Object.entries(currentProduct.specifications).map(([key, value], index) => {
                    if (key === 'certifications') return null;
                    const Icon = specificationIcons[key as keyof typeof specificationIcons] || Package;
                    return (
                      <div
                        key={key}
                        className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg"
                        data-aos="fade-up"
                        data-aos-delay={200 + (index * 100)}
                      >
                        <Icon className="text-emerald-600 mt-1" size={20} />
                        <div className="flex-1">
                          <h3 className="font-semibold text-emerald-800 capitalize mb-1">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </h3>
                          <p className="text-gray-700">{String(value)}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {selectedTab === 'certifications' && (
              <div className="space-y-6">
                <h2 
                  className="text-2xl font-bold text-emerald-800 mb-6"
                  data-aos="fade-up"
                  data-aos-delay="100"
                >
                  Certifications & Standards
                </h2>
                <div className="grid sm:grid-cols-2 gap-4">
                  {currentProduct.specifications?.certifications && Array.isArray(currentProduct.specifications.certifications) ?
                    currentProduct.specifications.certifications.map((cert: string, index: number) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-4 bg-emerald-50 rounded-lg"
                        data-aos="zoom-in"
                        data-aos-delay={200 + (index * 100)}
                      >
                        <CheckCircle className="text-emerald-600" size={20} />
                        <span className="font-medium text-emerald-800">{cert}</span>
                      </div>
                    )) : (
                      <div className="col-span-2 text-center text-gray-500 py-8">
                        No certifications available
                      </div>
                    )
                  }
                </div>
                <div 
                  className="mt-8 p-6 bg-yellow-50 rounded-xl border-l-4 border-yellow-400"
                  data-aos="fade-up"
                  data-aos-delay="500"
                >
                  <h3 className="font-semibold text-emerald-800 mb-2">Quality Assurance</h3>
                  <p className="text-gray-700">
                    All our products undergo rigorous quality control processes and meet international standards for export. 
                    Our certifications ensure compliance with global food safety and quality requirements.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <div 
              className="bg-emerald-50 p-6 rounded-xl border border-emerald-200"
              data-aos="fade-left"
              data-aos-delay="300"
            >
              <h3 className="font-bold text-emerald-800 mb-4">Quick Facts</h3>
              <div className="space-y-3">
                <div data-aos="fade-up" data-aos-delay="400">
                  <span className="text-sm text-gray-600">Minimum Order:</span>
                  <p className="font-semibold text-emerald-800">{currentProduct?.specifications?.moq || 'Contact for details'}</p>
                </div>
                <div data-aos="fade-up" data-aos-delay="500">
                  <span className="text-sm text-gray-600">Packaging:</span>
                  <p className="font-semibold text-emerald-800">{currentProduct?.specifications?.packaging || 'Standard packaging'}</p>
                </div>
                <div data-aos="fade-up" data-aos-delay="600">
                  <span className="text-sm text-gray-600">Delivery:</span>
                  <p className="font-semibold text-emerald-800">{currentProduct?.specifications?.leadTime || 'Contact for details'}</p>
                </div>
              </div>
            </div>

            <div 
              className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm"
              data-aos="fade-left"
              data-aos-delay="400"
            >
              <h3 className="font-bold text-emerald-800 mb-4">Need More Information?</h3>
              <p className="text-gray-600 mb-4">
                Contact our export team for detailed specifications, pricing, and shipping information.
              </p>
              <button 
                className="w-full px-4 py-3 bg-yellow-400 text-emerald-800 rounded-lg font-semibold hover:bg-yellow-300 transition-colors duration-200"
                data-aos="zoom-in"
                data-aos-delay="500"
              >
                Get Quote
              </button>
            </div>

            <div 
              className="bg-gradient-to-br from-emerald-800 to-emerald-700 text-white p-6 rounded-xl"
              data-aos="fade-left"
              data-aos-delay="500"
            >
              <h3 className="font-bold mb-2">Export Ready</h3>
              <p className="text-emerald-100 text-sm">
                All products are export-grade and ready for international shipping with proper documentation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductPage;