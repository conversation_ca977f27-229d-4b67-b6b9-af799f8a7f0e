"use client";

import React from "react";
import ModernHeroWithBackground from "../../../components/ModernHeroWithBackground";
import {
  Leaf,
  Users,
  Globe,
  ChevronRight,
  Sprout,
  TrendingUp,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
// Animated progress bar component




// Button component



interface InfoSectionData {
  title: string;
  image: {
    src: string;
    alt: string;
  };
  paragraphs: string[];
  ctaButton?: {
    href: string;
    text: string;
  };
}

interface InfoSectionProps {
  data: InfoSectionData;
  reverse?: boolean;
  className?: string;
  radiusOnLeft?: boolean; // new prop to control border radius side
}

function InfoSection({
  data,
  reverse = false,
  className = "",
  radiusOnLeft = false,
}: InfoSectionProps) {
  const borderRadiusClass = radiusOnLeft ? "rounded-l-full" : "rounded-r-full";

  return (
    <section
      className={`bg-white pr-4 lg:pr-8 py-8 sm:py-12 lg:py-16 relative overflow-hidden ${className}`}
      data-aos="fade-up"
      data-aos-duration="1000"
    >
      <div className="max-w-7xl mx-auto">
        <div
          className={`flex flex-col lg:flex-row gap-6 lg:gap-12 ${
            reverse ? "lg:flex-row-reverse" : ""
          }`}
        >
          <div
            data-aos={reverse ? "fade-left" : "fade-right"}
            data-aos-delay="200"
            data-aos-duration="1000"
            className={`relative w-full h-64 sm:h-80 lg:h-[520px] lg:w-[50%] ${borderRadiusClass} overflow-hidden shadow-none`}
          >
            <Image
              className="w-full h-full object-cover shadow-none"
              src={data.image.src}
              alt={data.image.alt}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center space-y-4 sm:space-y-6 px-4 sm:px-6 lg:px-12">
            <div
              data-aos={reverse ? "fade-right" : "fade-left"}
              data-aos-delay="400"
              data-aos-duration="800"
            >
              <h6 className="text-2xl sm:text-3xl lg:text-4xl font-baru-bold mb-3 sm:mb-4 text-earthy-cocoa">
                {data.title}
              </h6>
              {data.paragraphs.map((paragraph, index) => (
                <p
                  key={index}
                  className="text-sm sm:text-base text-gray-600 font-baru-regular leading-relaxed mb-3 sm:mb-4"
                  data-aos="fade-up"
                  data-aos-delay={600 + index * 100}
                  data-aos-duration="600"
                >
                  {paragraph}
                </p>
              ))}
            </div>
            {data.ctaButton && (
              <Link href={data.ctaButton.href}>
                <button
                  className="transition-all duration-300 transform hover:scale-105 w-fit mt-4 sm:mt-6 bg-earthy-cocoa hover:bg-earthy-cocoa text-white font-baru-semibold py-2 sm:py-3 px-6 sm:px-8 rounded-lg shadow-none text-sm sm:text-base"
                  data-aos="fade-up"
                  data-aos-delay="1000"
                >
                  {data.ctaButton.text}
                </button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}

export default function SustainabilityPage() {
 

  

  const sustainableFarmingData = {
    title: "Sustainable Farming Practices",
    image: {
      src: "/assets/portrait-african-american-worker-holding-fresh-green-lettuce-grown-hydroponic-controlled-enviroment-delivery-smiling-woman-showing-freshly-harvested-salad-grown-modern-greenhouse.jpg", // Replace with your actual image path
      alt: "Sustainable Farming Field",
    },
    paragraphs: [
      "We commit to eco-friendly agricultural methods that conserve natural resources and promote biodiversity.",
      "Our farms utilize advanced water management systems and organic fertilizers to enhance soil health.",
      "By integrating crop rotation and cover cropping, we ensure long-term productivity and environmental balance.",
    ],
    ctaButton: {
      href: "/sustainable-farming",
      text: "Learn More",
    },
  };

  const exportExcellenceData = {
    title: "Export Excellence",
    image: {
      src: "/assets/transportation-logistics-container-cargo-ship-cargo-plane-3d-rendering-illustration.jpg", // Replace with your actual image path
      alt: "Global Export",
    },
    paragraphs: [
      "Delivering premium quality products to international markets with strict adherence to global standards.",
      "Our logistics and supply chain solutions guarantee timely and safe delivery to customers worldwide.",
      "We foster strong relationships with partners to expand market reach and promote export growth.",
    ],
    ctaButton: {
      href: "/export-excellence",
      text: "Explore Our Exports",
    },
  };

  const communityEmpowermentData = {
    title: "Community Empowerment",
    image: {
      src: "/assets/portrait-people-team-agriculture-farm-with-tools-wheelbarrow-equipment-planting-diverse-group-man-woman-with-strategy-planning-collaboration-growth-sustainability.jpg", // Replace with your actual image path

      alt: "Community Engagement",
    },
    paragraphs: [
      "Empowering local communities through education, training, and sustainable development programs.",
      "We invest in social initiatives that improve livelihoods and foster economic independence.",
      "Collaboration with stakeholders ensures inclusive growth and shared prosperity.",
    ],
    ctaButton: {
      href: "/community-empowerment",
      text: "Get Involved",
    },
  };

  return (
    <>
      {/* Hero Banner */}
      <ModernHeroWithBackground
        title={
          <>
            Our <span className="text-yellow-400">Commitment </span>
            <br />
            to Sustainability
          </>
        }
        description={
          <>
            At AfricSource, sustainability is at the heart of everything we do.
            We believe in nurturing the land that nurtures us, ensuring a
            prosperous future for both farmers and the environment.
          </>
        }
        backgroundSrc="/assets/pexels-akilmazumder-1072824.jpg"
        primaryButtonText="Our Products"
        primaryButtonHref="/products"
        secondaryButtonText="Contact Us"
        secondaryButtonHref="/contact"
      />
      <InfoSection data={sustainableFarmingData} radiusOnLeft />
      <InfoSection data={exportExcellenceData} reverse />
      <InfoSection data={communityEmpowermentData} radiusOnLeft />

      {/* Impact Statistics */}
      <section
        className="py-20 px-4"
        style={{
          background: `linear-gradient(135deg, #212121 0%, #5D4037 100%)`,
        }}
      >
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16" data-aos="fade-up">
            <h3 className="text-4xl font-light text-white mb-4">
              Our Global Impact
            </h3>
            <p className="text-xl text-gray-300 font-light">
              Transforming agriculture, one farm at a time
            </p>
          </div>
          <div
            className="grid grid-cols-1 md:grid-cols-4 gap-8"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div className="text-center group">
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                style={{ backgroundColor: "#4CAF50" }}
              >
                <Users className="w-10 h-10 text-white" />
              </div>
              <div
                className="text-4xl font-light text-white mb-2"
                style={{ color: "#FFCA00" }}
              >
                500+
              </div>
              <div className="text-gray-300 font-light">Partner Farmers</div>
            </div>
            <div className="text-center group">
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                style={{ backgroundColor: "#5BD05F" }}
              >
                <TrendingUp className="w-10 h-10 text-white" />
              </div>
              <div
                className="text-4xl font-light text-white mb-2"
                style={{ color: "#FFCA00" }}
              >
                35%
              </div>
              <div className="text-gray-300 font-light">Carbon Reduction</div>
            </div>
            <div className="text-center group">
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                style={{ backgroundColor: "#FFCA00" }}
              >
                <Leaf className="w-10 h-10" style={{ color: "#212121" }} />
              </div>
              <div
                className="text-4xl font-light text-white mb-2"
                style={{ color: "#FFCA00" }}
              >
                15,000
              </div>
              <div className="text-gray-300 font-light">Trees Planted</div>
            </div>
            <div className="text-center group">
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                style={{ backgroundColor: "#4CAF50" }}
              >
                <Globe className="w-10 h-10 text-white" />
              </div>
              <div
                className="text-4xl font-light text-white mb-2"
                style={{ color: "#FFCA00" }}
              >
                25+
              </div>
              <div className="text-gray-300 font-light">Export Countries</div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 px-4" style={{ backgroundColor: "#FFFFFF" }}>
        <div
          className="max-w-4xl mx-auto text-center"
          data-aos="fade-up"
          data-aos-delay="400"
        >
          <div className="mb-8">
            <div
              className="w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl"
              style={{ backgroundColor: "#4CAF50" }}
            >
              <Leaf className="w-12 h-12 text-white" />
            </div>
          </div>
          <h3 className="text-4xl font-light mb-8" style={{ color: "#212121" }}>
            Join Our Sustainability Journey
          </h3>
          <p className="text-xl text-gray-600 mb-12 font-light max-w-2xl mx-auto">
            Partner with us to create a more sustainable future for African
            agriculture and global food security.
          </p>
          <button
            className="group inline-flex items-center px-12 py-5 text-white font-semibold rounded-full hover:shadow-2xl transition-all duration-500 transform hover:scale-105 text-lg"
            style={{ backgroundColor: "#4CAF50" }}
          >
            Explore Our Sustainability Initiatives
            <ChevronRight className="ml-3 w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
          </button>
        </div>
      </section>

      {/* Bottom Banner */}
      <section
        className="py-12 px-4"
        style={{
          background: "linear-gradient(90deg, #5BD05F 0%, #4CAF50 100%)",
        }}
      >
        <div className="max-w-6xl mx-auto text-center" data-aos="fade-up">
          <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8">
            <div className="flex items-center space-x-3">
              <Sprout className="w-8 h-8 text-white" />
              <span className="text-white font-baru-semibold text-lg">
                Sustainable Farming
              </span>
            </div>
            <div className="hidden md:block w-px h-8 bg-white opacity-30"></div>
            <div className="flex items-center space-x-3">
              <Globe className="w-8 h-8 text-white" />
              <span className="text-white font-baru-semibold text-lg">
                Global Export
              </span>
            </div>
            <div className="hidden md:block w-px h-8 bg-white opacity-30"></div>
            <div className="flex items-center space-x-3">
              <Users className="w-8 h-8 text-white" />
              <span className="text-white font-baru-semibold text-lg">
                Community Impact
              </span>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
