"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { ProductForm } from "@/components/molecules/ProductForm";

export default function NewProductPage() {
  const router = useRouter();
  const [, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData) => {
    setIsSubmitting(true);
    setError(null);
      console.log("Form submitted successfully:", formData);

    try {
      // Prepare the data for API submission
      const productData = {
        name: formData.name,
        description: formData.description,
        details: formData.details,
        price: formData.price,
        stock: formData.stock,
        categoryIds: formData.categories, // API expects categoryIds
        tagIds: formData.tags, // API expects tagIds
        specifications: Object.entries(formData.specifications)
          .filter(([key, value]) => value && value.toString().trim() !== '') // Filter out empty values
          .map(([key, value]) => ({
            key,
            value: Array.isArray(value) ? value.join(', ') : value.toString(),
          })),
        images: formData.images && formData.images.length > 0
          ? formData.images.map(img => ({
              url: img.url,
              alt: img.alt || formData.name,
              isFeatured: img.isFeatured || false,
              isMain: img.isMain || false,
              isPrimary: img.isPrimary || false,
              order: img.order || 0
            }))
          : [], // Use the new images array format
      };

      // Send data to API
      const response = await fetch("/api/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(productData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create product");
      }

      // Redirect to products page after successful submission
      router.push("/dashboard/products");
    } catch (err) {
      console.error("Error submitting form:", err);
      setError(
        err.message ||
          "An error occurred while creating the product. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      {/* Header Section with Subtle Primary Color */}
      <div className="mb-8 p-6 bg-gradient-to-r from-green-800 to-green-700 rounded-2xl shadow-lg">
        <h1 className="text-3xl font-bold text-white mb-2">Add New Product</h1>
        <p className="text-green-100">
          Create a new product in your catalog and expand your inventory
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Main Form Container */}
      <div className="bg-white rounded-xl shadow-lg border border-yellow-200 overflow-hidden">
        <div className="p-6">
          <ProductForm onSubmit={handleSubmit} />
        </div>
      </div>
    </DashboardLayout>
  );
}
