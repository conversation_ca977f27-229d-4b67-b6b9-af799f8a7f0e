"use client";

import React, { useState, useEffect, useCallback } from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import Link from 'next/link';
import Image from 'next/image';

// Types
interface Category {
  id: string;
  name: string;
}

interface ProductImage {
  $id: string;
  url: string;
  alt?: string;
  isMain: boolean;
  isFeatured: boolean;
  order: number;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price?: string;
  stock: 'Available' | 'Limited' | 'Out of Stock';
  categories?: Category[];
  images?: ProductImage[];
}

// Constants
const FALLBACK_IMAGE = '/globe.svg';

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('name-asc');
  const [categories, setCategories] = useState<string[]>(['All']);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch products and categories from API
  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch both products and categories in parallel
      const [productsResponse, categoriesResponse] = await Promise.all([
        fetch('/api/products'),
        fetch('/api/categories')
      ]);

      if (!productsResponse.ok) {
        throw new Error(`Failed to fetch products: ${productsResponse.status} ${productsResponse.statusText}`);
      }
      
      if (!categoriesResponse.ok) {
        throw new Error(`Failed to fetch categories: ${categoriesResponse.status} ${categoriesResponse.statusText}`);
      }

      const [productsData, categoriesData] = await Promise.all([
        productsResponse.json(),
        categoriesResponse.json()
      ]);

      setProducts(productsData);
      setCategories(['All', ...categoriesData.map((cat: Category) => cat.name)]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching data';
      console.error('Error fetching data:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Filter and sort products
  const filteredProducts = React.useMemo(() => {
    return products
      .filter(product => {
        // Apply search filter
        if (searchQuery) {
          const query = searchQuery.toLowerCase().trim();
          const matchesName = product.name.toLowerCase().includes(query);
          const matchesDescription = product.description.toLowerCase().includes(query);
          const matchesCategory = product.categories?.some(cat => 
            cat.name.toLowerCase().includes(query)
          );
          
          if (!matchesName && !matchesDescription && !matchesCategory) {
            return false;
          }
        }

        // Apply category filter
        if (selectedCategory !== 'All') {
          if (!product.categories?.some(cat => cat.name === selectedCategory)) {
            return false;
          }
        }

        return true;
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'name-asc':
            return a.name.localeCompare(b.name);
          case 'name-desc':
            return b.name.localeCompare(a.name);
          case 'category':
            const aCategory = a.categories?.[0]?.name || '';
            const bCategory = b.categories?.[0]?.name || '';
            return aCategory.localeCompare(bCategory);
          default:
            return 0;
        }
      });
  }, [products, searchQuery, selectedCategory, sortBy]);

  // Delete product handler
  const handleDeleteProduct = async (productId: string, productName: string) => {
    const confirmMessage = `Are you sure you want to delete "${productName}"? This action cannot be undone.`;
    
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to delete product: ${response.status} ${response.statusText}`);
      }

      // Remove product from state
      setProducts(prevProducts => prevProducts.filter(product => product.$id !== productId));
      
      // Show success message (you might want to use a toast notification instead)
      alert(`Product "${productName}" has been successfully deleted.`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while deleting the product';
      console.error('Error deleting product:', err);
      alert(errorMessage);
    }
  };

  // Get stock status styling
  const getStockStatusStyle = (stock: Product['stock']) => {
    switch (stock) {
      case 'Available':
        return 'bg-green-50 text-green-700 border border-green-200';
      case 'Limited':
        return 'bg-yellow-50 text-yellow-700 border border-yellow-200';
      case 'Out of Stock':
        return 'bg-red-50 text-red-700 border border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border border-gray-200';
    }
  };

  // Retry handler for error state
  const handleRetry = () => {
    fetchData();
  };

  return (
    <DashboardLayout>
      {/* Header Section with Gradient Background */}
      <div className="mb-8 p-6 bg-gradient-to-r from-green-800 to-green-700 rounded-2xl shadow-lg">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Products Management</h1>
            <p className="text-green-100 text-lg">
              Manage your product catalog • {products.length} product{products.length !== 1 ? 's' : ''} total
            </p>
          </div>
                    <Link href='/dashboard/products/new' className="mt-4 md:mt-0">

            <button className="bg-yellow-400 hover:bg-yellow-300 text-green-900 font-semibold px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <span className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Add New Product</span>
              </span>
            </button>
          </Link>
        </div>
      </div>

      {/* Enhanced Filters and Search */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Search with Enhanced Styling */}
          <div className="relative group">
            <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
              <svg className="w-5 h-5 text-green-600 group-focus-within:text-yellow-500 transition-colors" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
              </svg>
            </div>
            <input
              type="search"
              className="block w-full py-3 pl-12 pr-4 text-gray-900 bg-gray-50 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-yellow-400 focus:border-green-600 focus:bg-white transition-all duration-200"
              placeholder="Search products, categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              aria-label="Search products"
            />
          </div>

          {/* Category Filter with Custom Styling */}
          <div>
            <label htmlFor="category-filter" className="sr-only">Filter by category</label>
            <select
              id="category-filter"
              className="bg-gray-50 border-2 border-gray-200 text-gray-900 rounded-xl focus:ring-2 focus:ring-yellow-400 focus:border-green-600 focus:bg-white block w-full py-3 px-4 transition-all duration-200 appearance-none cursor-pointer"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Sort By with Custom Styling */}
          <div>
            <label htmlFor="sort-by" className="sr-only">Sort products by</label>
            <select
              id="sort-by"
              className="bg-gray-50 border-2 border-gray-200 text-gray-900 rounded-xl focus:ring-2 focus:ring-yellow-400 focus:border-green-600 focus:bg-white block w-full py-3 px-4 transition-all duration-200 appearance-none cursor-pointer"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="category">Category</option>
            </select>
          </div>
        </div>
        
        {/* Enhanced Search Results Summary */}
        {searchQuery && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-xl">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <p className="text-green-800 font-medium">
                {filteredProducts.length > 0 
                  ? `Found ${filteredProducts.length} result${filteredProducts.length !== 1 ? 's' : ''} for "${searchQuery}"`
                  : `No results found for "${searchQuery}"`
                }
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Loading State */}
      {isLoading && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-12 flex justify-center items-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-green-100"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-yellow-400 absolute top-0 left-0"></div>
            </div>
            <p className="text-green-700 font-medium text-lg">Loading your products...</p>
          </div>
        </div>
      )}

      {/* Enhanced Error State */}
      {error && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-xl">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-semibold text-red-800">Unable to load products</h3>
                <p className="text-red-700 mt-2">{error}</p>
                <button
                  onClick={handleRetry}
                  className="mt-4 bg-red-100 hover:bg-red-200 text-red-800 font-medium px-4 py-2 rounded-lg transition-colors duration-200"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Products Table */}
      {!isLoading && !error && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gradient-to-r from-green-50 to-green-100 border-b border-green-200">
                <tr>
                  <th scope="col" className="px-8 py-4 text-left text-sm font-bold text-green-800 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-8 py-4 text-left text-sm font-bold text-green-800 uppercase tracking-wider">
                    Category
                  </th>
                  <th scope="col" className="px-8 py-4 text-left text-sm font-bold text-green-800 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-8 py-4 text-left text-sm font-bold text-green-800 uppercase tracking-wider">
                    Stock Status
                  </th>
                  <th scope="col" className="px-8 py-4 text-right text-sm font-bold text-green-800 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product, index) => (
                    <tr key={product.$id} className={`hover:bg-green-50 transition-all duration-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                      <td className="px-8 py-6 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-14 w-14 relative">
                            <Image
                              src={
                                product.images?.find(img => img.isMain)?.url ||
                                product.images?.[0]?.url ||
                                FALLBACK_IMAGE
                              }
                              alt={
                                product.images?.find(img => img.isMain)?.alt ||
                                product.images?.[0]?.alt ||
                                product.name
                              }
                              fill
                              className="rounded-xl object-cover border-2 border-gray-200"
                              sizes="56px"
                            />
                          </div>
                          <div className="ml-6 min-w-0 flex-1">
                            <div className="text-lg font-semibold text-gray-900 truncate">{product.name}</div>
                            <div className="text-sm text-gray-600 line-clamp-2 mt-1">{product.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-8 py-6 whitespace-nowrap">
                        <div className="flex flex-wrap gap-2">
                          {product.categories && product.categories.length > 0 ? (
                            product.categories.map((category) => (
                              <span
                                key={category.$id}
                                className="px-3 py-1.5 text-xs font-semibold rounded-full bg-green-100 text-green-800 border border-green-200"
                              >
                                {category.name}
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-400 text-sm italic">No category assigned</span>
                          )}
                        </div>
                      </td>
                      <td className="px-8 py-6 whitespace-nowrap">
                        <div className="text-lg font-semibold text-gray-900">
                          {product.price || (
                            <span className="text-gray-500 italic">Contact for pricing</span>
                          )}
                        </div>
                      </td>
                      <td className="px-8 py-6 whitespace-nowrap">
                        <span className={`px-3 py-2 text-sm font-semibold rounded-full ${getStockStatusStyle(product.stock)}`}>
                          {product.stock}
                        </span>
                      </td>
                      <td className="px-8 py-6 whitespace-nowrap text-right">
                        <div className="flex justify-end space-x-3">
                          <Link
                            href={`/dashboard/products/edit/${product.$id}`}
                            className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                          >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDeleteProduct(product.$id, product.name)}
                            className="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                            type="button"
                          >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-8 py-16 text-center">
                      <div className="flex flex-col items-center space-y-6">
                        <div className="relative">
                          <svg className="w-20 h-20 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                          </svg>
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-green-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                          </div>
                        </div>
                        <div className="text-center">
                          <p className="text-xl font-semibold text-gray-700">No products found</p>
                          <p className="text-gray-500 mt-2 max-w-md">
                            {searchQuery || selectedCategory !== 'All' 
                              ? 'Try adjusting your search terms or filter settings to find what you\'re looking for.' 
                              : 'Ready to showcase your amazing products? Add your first product to get started.'
                            }
                          </p>
                        </div>
                        {!searchQuery && selectedCategory === 'All' && (
                    <Link href='/dashboard/products/new' className="mt-4 md:mt-0">

                          <button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold px-8 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            <span className="flex items-center space-x-2">
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              <span>Add Your First Product</span>
                            </span>
                          </button>
                          </Link>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}