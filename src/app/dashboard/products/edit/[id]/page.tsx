"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { ProductForm } from "@/components/molecules/ProductForm";
import { Button } from "@/components/atoms/Button";

// Sample product data (in a real app, this would come from an API)

export default function EditProductPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const actualParams = React.use(params);
  const id = actualParams.id;

  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch product data from API
        const response = await fetch(`/api/products/${id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError("Product not found");
          } else {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch product");
          }
          return;
        }

        const productData = await response.json();
        setProduct(productData);
      } catch (err) {
        console.error("Error fetching product:", err);
        setError(
          err.message ||
            "An error occurred while fetching the product. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const handleSubmit = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Prepare the data for API submission
      

      // Send data to API
     

      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.error || 'Failed to update product');
      // }

      // Redirect to products page after successful submission
      router.push("/dashboard/products");
    } catch (err) {
      console.error("Error updating product:", err);
      setError(
        err?.message ||
          "An error occurred while updating the product. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout>
      {/* Header Section with Subtle Primary Color */}
      <div className="mb-8 p-6 rounded-xl bg-emerald-50 border border-emerald-200 shadow-sm">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-emerald-900 mb-2">
              Edit Product
            </h1>
            <p className="text-emerald-700">
              Update product information and specifications
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Button
              variant="outline"
              size="md"
              href="/dashboard/products"
              className="border-emerald-300 text-emerald-700 hover:bg-emerald-100"
            >
              Back to Products
            </Button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Main Form Container */}
      <div className="bg-white rounded-xl shadow-lg border border-yellow-200 overflow-hidden">
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"></div>
            </div>
          ) : product ? (
            <ProductForm
              initialData={product}
              onSubmit={handleSubmit}
              isEditMode={true}
            />
          ) : (
            <div className="text-center py-12">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-8 max-w-md mx-auto">
                <h3 className="text-lg font-medium text-emerald-900 mb-2">
                  Product Not Found
                </h3>
                <p className="text-emerald-700 mb-6">
                  {"The product you're looking for doesn't exist or has been removed."}
                </p>
                <Button
                  variant="primary"
                  href="/dashboard/products"
                  className="bg-emerald-700 hover:bg-emerald-800 text-white"
                >
                  Back to Products
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
