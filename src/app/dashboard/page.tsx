"use client";

import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import Link from 'next/link';
export default function DashboardPage() {
  // Sample data for dashboard metrics
  // const metrics = [
  //   { 
  //     title: 'Total Products', 
  //     value: '24', 
  //     change: '+3', 
  //     changeType: 'positive',
  //     icon: (
  //       <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
  //       </svg>
  //     )
  //   },
  //   { 
  //     title: 'Active Orders', 
  //     value: '12', 
  //     change: '+2', 
  //     changeType: 'positive',
  //     icon: (
  //       <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
  //       </svg>
  //     )
  //   },
  //   { 
  //     title: 'Customers', 
  //     value: '48', 
  //     change: '+5', 
  //     changeType: 'positive',
  //     icon: (
  //       <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
  //       </svg>
  //     )
  //   },
  //   { 
  //     title: 'Revenue (MTD)', 
  //     value: '$24,500', 
  //     change: '+12%', 
  //     changeType: 'positive',
  //     icon: (
  //       <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  //       </svg>
  //     )
  //   },
  // ];

  // Sample data for recent activities
  const recentActivities = [
    { 
      id: 1, 
      action: 'New order received', 
      details: 'Order #1234 - Cashew Nuts (2 tons)', 
      time: '2 hours ago',
      type: 'order',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      )
    },
    { 
      id: 2, 
      action: 'Product updated', 
      details: 'Shea Butter - Price updated', 
      time: '5 hours ago',
      type: 'product',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      )
    },
    { 
      id: 3, 
      action: 'New customer registered', 
      details: 'Global Foods Inc.', 
      time: '1 day ago',
      type: 'customer',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      )
    },
    { 
      id: 4, 
      action: 'Shipment dispatched', 
      details: 'Order #1230 - Sesame Seeds (5 tons)', 
      time: '2 days ago',
      type: 'shipment',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
        </svg>
      )
    },
    { 
      id: 5, 
      action: 'Inventory updated', 
      details: 'Cocoa Beans - 10 tons added', 
      time: '3 days ago',
      type: 'inventory',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      )
    },
  ];

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'order': return 'bg-green-100 text-green-600';
      case 'product': return 'bg-blue-100 text-blue-600';
      case 'customer': return 'bg-purple-100 text-purple-600';
      case 'shipment': return 'bg-yellow-100 text-yellow-600';
      case 'inventory': return 'bg-gray-100 text-gray-600';
      default: return 'bg-green-100 text-green-600';
    }
  };

  return (
    <DashboardLayout>
      {/* Header Section with Gradient Background */}
      <div className="mb-8 p-6 bg-gradient-to-r from-green-800 to-green-700 rounded-2xl shadow-lg">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
            <p className="text-green-100 text-lg">{"Welcome back, Admin! Here's what's happening today."}</p>
          </div>
          <Link href='/dashboard/products/new' className="mt-4 md:mt-0">
            <button className="bg-yellow-400 hover:bg-yellow-300 text-green-900 font-semibold px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <span className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Add New Product</span>
              </span>
            </button>
          </Link>
        </div>
      </div>

      {/* Enhanced Metrics Cards */}
     {/* <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  {metrics.map((metric, index) => (
    <div
      key={index}
      className="relative rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-white to-green-50"
      style={{
        backgroundImage: "repeating-linear-gradient(45deg, rgba(0,0,0,0.03) 0 10px, transparent 10px 20px)"
      }}
    >
      <div className="flex items-center justify-between mb-5">
        <div className="p-3 rounded-2xl bg-green-100 text-green-700 shadow-inner group-hover:scale-110 transition-transform">
          <div className="text-xl">{metric.icon}</div>
        </div>
        <span
          className={`px-3 py-1 text-xs font-medium rounded-full border ${
            metric.changeType === 'positive'
              ? 'bg-green-100 text-green-700 border-green-200'
              : 'bg-red-100 text-red-700 border-red-200'
          }`}
        >
          {metric.change}
        </span>
      </div>
      <h3 className="text-gray-600 text-sm font-medium mb-1">{metric.title}</h3>
      <div className="text-3xl font-bold text-gray-900 tracking-tight">{metric.value}</div>
    </div>
  ))}
</div> */}



      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Enhanced Recent Activities */}
        <div className="lg:col-span-2 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Recent Activities</h2>
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          </div>
          <div className="space-y-6">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-green-50 transition-all duration-200">
                <div className={`p-2.5 rounded-xl ${getActivityColor(activity.type)} group-hover:scale-110 transition-transform duration-200`}>
                  {activity.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-gray-900 text-lg">{activity.action}</h4>
                  <p className="text-gray-600 mt-1">{activity.details}</p>
                  <div className="flex items-center mt-2">
                    <svg className="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm text-gray-500">{activity.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-8 text-center">
            <button className="inline-flex items-center px-6 py-3 text-green-700 bg-green-100 hover:bg-green-200 font-medium rounded-xl transition-all duration-200 transform hover:scale-105">
              <span>View All Activities</span>
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Enhanced Quick Links */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          <div className="space-y-4">
            <button className="w-full group flex items-center p-4 bg-gradient-to-r from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border-2 border-green-200 hover:border-green-300 rounded-xl transition-all duration-200 transform hover:scale-105">
              <div className="p-2 bg-green-600 text-white rounded-lg group-hover:bg-green-700 transition-colors">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <div className="ml-4 text-left">
                <div className="font-semibold text-gray-900 text-lg">Manage Products</div>
                <div className="text-sm text-gray-600">Add, edit, or organize your catalog</div>
              </div>
              <svg className="w-5 h-5 text-green-600 ml-auto group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* <button className="w-full group flex items-center p-4 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-2 border-blue-200 hover:border-blue-300 rounded-xl transition-all duration-200 transform hover:scale-105">
              <div className="p-2 bg-blue-600 text-white rounded-lg group-hover:bg-blue-700 transition-colors">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <div className="ml-4 text-left">
                <div className="font-semibold text-gray-900 text-lg">View Orders</div>
                <div className="text-sm text-gray-600">Track and manage customer orders</div>
              </div>
              <svg className="w-5 h-5 text-blue-600 ml-auto group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button> */}

            {/* <button className="w-full group flex items-center p-4 bg-gradient-to-r from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border-2 border-purple-200 hover:border-purple-300 rounded-xl transition-all duration-200 transform hover:scale-105">
              <div className="p-2 bg-purple-600 text-white rounded-lg group-hover:bg-purple-700 transition-colors">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <div className="ml-4 text-left">
                <div className="font-semibold text-gray-900 text-lg">Manage Customers</div>
                <div className="text-sm text-gray-600">View and organize customer data</div>
              </div>
              <svg className="w-5 h-5 text-purple-600 ml-auto group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button> */}

            <button className="w-full group flex items-center p-4 bg-gradient-to-r from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 border-2 border-yellow-200 hover:border-yellow-300 rounded-xl transition-all duration-200 transform hover:scale-105">
              <div className="p-2 bg-yellow-600 text-white rounded-lg group-hover:bg-yellow-700 transition-colors">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div className="ml-4 text-left">
                <div className="font-semibold text-gray-900 text-lg">Settings</div>
                <div className="text-sm text-gray-600">Configure system preferences</div>
              </div>
              <svg className="w-5 h-5 text-yellow-600 ml-auto group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Quick Stats */}
          <div className="mt-8 p-4 bg-gradient-to-r from-green-50 to-yellow-50 rounded-xl border-2 border-green-100">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-800">🎉</div>
              <div className="text-lg font-semibold text-green-800 mt-2">Great Progress!</div>
              <div className="text-sm text-green-700 mt-1">{"You've grown by 15% this month"}</div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}