

import {aboutSectionData,partnershipSectionData,careersSectionData } from "@/store";
import React, { JSX } from "react";

// Types for better type safety
interface Video {
  src: string;
  title: string;
  description?: string;
}

interface TrustIndicator {
  value: string;
  label: string;
  icon?: React.ComponentType;
}

interface FloatingElement {
  position: { top: string; left: string };
  icon: React.ComponentType;
  className: string;
  delay: number;
  duration: number;
}

interface Button {
  text: string;
  href: string;
  variant: 'primary' | 'secondary';
  icon?: React.ComponentType;
}

interface HeroContent {
  subtitle: string;
  title: {
    prefix: string;
    highlight: string;
    suffix: string;
  };
  description: string;
  buttons: Button[];
}

interface HeroSectionProps {
  content?: HeroContent;
  videos?: Video[];
  backgroundImage?: string;
  trustIndicators?: TrustIndicator[];
  floatingElements?: FloatingElement[];
  autoPlayInterval?: number;
  transitionDuration?: number;
  particleCount?: number;
  showVideoControls?: boolean;
  showTrustIndicators?: boolean;
  className?: string;
}



type HomePageProps = {
  aboutData?: typeof aboutSectionData;
  partnershipData?: typeof partnershipSectionData;
  careersData?: typeof careersSectionData;
  showHero?: boolean;
  showProductRange?: boolean;
  customSections?: React.ComponentType[];
};

// Type definitions
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

interface ContactInfo {
  type: 'location' | 'email' | 'phone' | 'hours';
  title: string;
  primary: string;
  secondary?: string;
  extra?: string;
  icon: JSX.Element;
  badge?: string;
}

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  message: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  company?: string;
  message?: string;
}

interface ContactPageConfig {
  hero: {
    title: JSX.Element;
    description: JSX.Element;
    backgroundSrc: string;
    primaryButton: {
      text: string;
      href: string;
    };
    secondaryButton: {
      text: string;
      href: string;
    };
  };
  contactForm: {
    title: string;
    subtitle: string;
    responseTime: string;
  };
  contactInfo: {
    title: string;
    subtitle: string;
  };
  locationSection: {
    imageSrc: string;
    imageAlt: string;
    title: string;
    description: string;
  };
  faqSection: {
    title: string;
    subtitle: string;
    ctaTitle: string;
    ctaDescription: string;
    ctaButtonText: string;
  };
  colors: {
    primary: string;
    accent: string;
    base: string;
  };
}
export type {
  Video,
  TrustIndicator,
  FloatingElement,
  Button,
  HeroContent,
  HeroSectionProps,
  HomePageProps,
  ContactPageConfig,
  ContactInfo, // ✅ Correct type name
  FormErrors,
  FormData,
  FAQ
};