import { AppwriteDocument, UserRole, ContactStatus } from '@/lib/appwrite';

// User document type
export interface UserDocument extends AppwriteDocument {
  name: string;
  email: string;
  password: string;
  role: UserR<PERSON>;
}

// Product document type
export interface ProductDocument extends AppwriteDocument {
  name: string;
  slug: string;
  description: string;
  details?: string;
  price?: string;
  stock: string;
  createdById: string;
}

// Product Image document type (current schema)
export interface ProductImageDocument extends AppwriteDocument {
  url: string;
  alt?: string;
  isFeatured: boolean;
  productId: string;
}

// Enhanced Product Image document type (for future schema)
export interface EnhancedProductImageDocument extends ProductImageDocument {
  isMain: boolean;
  isPrimary: boolean;
  order: number;
}

// Category document type
export interface CategoryDocument extends AppwriteDocument {
  name: string;
  description?: string;
}

// Tag document type
export interface TagDocument extends AppwriteDocument {
  name: string;
}

// Specification document type
export interface SpecificationDocument extends AppwriteDocument {
  key: string;
  value: any; // JSON value
  productId: string;
}

// Contact Submission document type
export interface ContactSubmissionDocument extends AppwriteDocument {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
  status: ContactStatus;
}

// Product-Category relationship document type
export interface ProductCategoryDocument extends AppwriteDocument {
  productId: string;
  categoryId: string;
}

// Product-Tag relationship document type
export interface ProductTagDocument extends AppwriteDocument {
  productId: string;
  tagId: string;
}

// Extended types with relationships (for API responses)
export interface ProductWithRelations extends ProductDocument {
  images: ProductImageDocument[];
  categories: CategoryDocument[];
  tags: TagDocument[];
  specifications: SpecificationDocument[];
  createdBy?: {
    $id: string;
    name: string;
    email: string;
  };
}

// Create/Update request types
export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  role?: UserRole;
}

export interface CreateProductRequest {
  name: string;
  description: string;
  details?: string;
  price?: string;
  stock?: string;
  categoryIds?: string[];
  tagIds?: string[];
  images?: Array<{
    url: string;
    alt?: string;
    isFeatured?: boolean;
    isMain?: boolean;
    isPrimary?: boolean;
    order?: number;
  }>;
  specifications?: Array<{
    key: string;
    value: any;
  }>;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  $id: string;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
}

export interface CreateTagRequest {
  name: string;
}

export interface CreateContactSubmissionRequest {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
}

// File upload types
export interface FileUploadResponse {
  $id: string;
  name: string;
  signature: string;
  mimeType: string;
  sizeOriginal: number;
  chunksTotal: number;
  chunksUploaded: number;
}

// Query options
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}
