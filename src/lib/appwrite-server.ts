import { createServerC<PERSON>, DAT<PERSON>ASE_ID, COLLECTIONS, Query, ID } from '@/lib/appwrite';
import {
  UserDocument,
  ProductDocument,
  ProductImageDocument,
  CategoryDocument,
  TagDocument,
  SpecificationDocument,
  ContactSubmissionDocument,
  ProductCategoryDocument,
  ProductTagDocument,
  ProductWithRelations,
  CreateUserRequest,
  CreateProductRequest,
  UpdateProductRequest,
  CreateCategoryRequest,
  CreateTagRequest,
  CreateContactSubmissionRequest,
  QueryOptions
} from '@/types/appwrite';
import { UserRole, ContactStatus } from '@/lib/appwrite';

// Initialize server client
const { databases, account, storage } = createServerClient();

// User operations
export class AppwriteUserService {
  static async createUser(userData: CreateUserRequest): Promise<UserDocument> {
    try {
      const user = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        ID.unique(),
        {
          name: userData.name,
          email: userData.email,
          password: userData.password,
          role: userData.role || UserRole.USER,
        }
      );
      return user as UserDocument;
    } catch (error) {
      console.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  static async getUserById(userId: string): Promise<UserDocument | null> {
    try {
      const user = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
      return user as UserDocument;
    } catch (error) {
      console.error('Error fetching user:', error);
      return null;
    }
  }

  static async getUserByEmail(email: string): Promise<UserDocument | null> {
    try {
      const users = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.USERS,
        [Query.equal('email', email)]
      );
      return users.documents.length > 0 ? users.documents[0] as UserDocument : null;
    } catch (error) {
      console.error('Error fetching user by email:', error);
      return null;
    }
  }

  static async getAllUsers(options: QueryOptions = {}): Promise<UserDocument[]> {
    try {
      const queries = [];
      
      if (options.limit) queries.push(Query.limit(options.limit));
      if (options.offset) queries.push(Query.offset(options.offset));
      if (options.orderBy) {
        const direction = options.orderDirection === 'asc' ? Query.orderAsc : Query.orderDesc;
        queries.push(direction(options.orderBy));
      }

      const users = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.USERS,
        queries
      );
      return users.documents as UserDocument[];
    } catch (error) {
      console.error('Error fetching users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  static async updateUser(userId: string, userData: Partial<CreateUserRequest>): Promise<UserDocument> {
    try {
      const user = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        userId,
        userData
      );
      return user as UserDocument;
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  }

  static async deleteUser(userId: string): Promise<boolean> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        userId
      );
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }
}

// Product operations
export class AppwriteProductService {
  static async createProduct(productData: CreateProductRequest, createdById: string): Promise<ProductDocument> {
    try {
      // Generate slug from name
      const slug = productData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      
      const product = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        ID.unique(),
        {
          name: productData.name,
          slug,
          description: productData.description,
          details: productData.details || '',
          price: productData.price || '',
          stock: productData.stock || 'Available',
          createdById,
        }
      );
      return product as ProductDocument;
    } catch (error) {
      console.error('Error creating product:', error);
      throw new Error('Failed to create product');
    }
  }

  static async getProductById(productId: string): Promise<ProductWithRelations | null> {
    try {
      const product = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        productId
      );

      // Fetch related data
      const [images, specifications, productCategories, productTags] = await Promise.all([
        this.getProductImages(productId),
        this.getProductSpecifications(productId),
        this.getProductCategories(productId),
        this.getProductTags(productId)
      ]);

      return {
        ...product,
        images,
        specifications,
        categories: productCategories,
        tags: productTags
      } as ProductWithRelations;
    } catch (error) {
      console.error('Error fetching product:', error);
      return null;
    }
  }

  static async getAllProducts(options: QueryOptions = {}): Promise<ProductDocument[]> {
    try {
      const queries = [];
      
      if (options.limit) queries.push(Query.limit(options.limit));
      if (options.offset) queries.push(Query.offset(options.offset));
      if (options.orderBy) {
        const direction = options.orderDirection === 'asc' ? Query.orderAsc : Query.orderDesc;
        queries.push(direction(options.orderBy));
      }
      // Note: Search functionality disabled due to fulltext index requirement
      // For now, we'll handle search client-side or implement it differently
      // if (options.search) {
      //   queries.push(Query.search('name', options.search));
      // }

      const products = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        queries
      );
      return products.documents as ProductDocument[];
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  static async updateProduct(productId: string, productData: Partial<CreateProductRequest>): Promise<ProductDocument> {
    try {
      // Prepare basic product data
      const updateData: any = {
        name: productData.name,
        description: productData.description,
        details: productData.details,
        price: productData.price,
        stock: productData.stock
      };

      // Update slug if name changed
      if (productData.name) {
        updateData.slug = productData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      }

      // Update the main product document
      const product = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        productId,
        updateData
      );

      // Handle images update
      if (productData.images) {
        // Delete existing images
        await this.deleteProductImages(productId);

        // Create new images
        for (const imageData of productData.images) {
          await this.createProductImage({
            url: imageData.url,
            alt: imageData.alt,
            isFeatured: imageData.isFeatured || false,
            isMain: imageData.isMain || false,
            isPrimary: imageData.isPrimary || false,
            order: imageData.order || 0,
            productId
          });
        }
      }

      // Handle categories update
      if (productData.categoryIds) {
        await this.updateProductCategories(productId, productData.categoryIds);
      }

      // Handle tags update
      if (productData.tagIds) {
        await this.updateProductTags(productId, productData.tagIds);
      }

      // Handle specifications update
      if (productData.specifications) {
        await this.updateProductSpecifications(productId, productData.specifications);
      }

      return product as ProductDocument;
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  static async deleteProduct(productId: string): Promise<boolean> {
    try {
      // Delete related data first
      await Promise.all([
        this.deleteProductImages(productId),
        this.deleteProductSpecifications(productId),
        this.deleteProductRelationships(productId)
      ]);

      // Delete the product
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        productId
      );
      return true;
    } catch (error) {
      console.error('Error deleting product:', error);
      return false;
    }
  }

  // Helper methods for product relationships
  static async getProductImages(productId: string): Promise<ProductImageDocument[]> {
    try {
      const images = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_IMAGES,
        [Query.equal('productId', productId)]
      );
      return images.documents as ProductImageDocument[];
    } catch (error) {
      console.error('Error fetching product images:', error);
      return [];
    }
  }

  static async createProductImage(imageData: {
    url: string;
    alt?: string;
    isFeatured?: boolean;
    isMain?: boolean;
    isPrimary?: boolean;
    order?: number;
    productId: string;
  }): Promise<ProductImageDocument> {
    try {
      const image = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_IMAGES,
        ID.unique(),
        {
          url: imageData.url,
          alt: imageData.alt || '',
          isFeatured: imageData.isFeatured || false,
          isMain: imageData.isMain || false,
          isPrimary: imageData.isPrimary || false,
          order: imageData.order || 0,
          productId: imageData.productId
        }
      );
      return image as ProductImageDocument;
    } catch (error) {
      console.error('Error creating product image:', error);
      throw error;
    }
  }

  static async updateProductImage(imageId: string, imageData: {
    url?: string;
    alt?: string;
    isFeatured?: boolean;
    isMain?: boolean;
    isPrimary?: boolean;
    order?: number;
  }): Promise<ProductImageDocument> {
    try {
      const image = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_IMAGES,
        imageId,
        imageData
      );
      return image as ProductImageDocument;
    } catch (error) {
      console.error('Error updating product image:', error);
      throw error;
    }
  }

  static async deleteProductImage(imageId: string): Promise<boolean> {
    try {
      await databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, imageId);
      return true;
    } catch (error) {
      console.error('Error deleting product image:', error);
      return false;
    }
  }

  static async getProductSpecifications(productId: string): Promise<SpecificationDocument[]> {
    try {
      const specs = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.SPECIFICATIONS,
        [Query.equal('productId', productId)]
      );
      return specs.documents as SpecificationDocument[];
    } catch (error) {
      console.error('Error fetching product specifications:', error);
      return [];
    }
  }

  static async getProductCategories(productId: string): Promise<CategoryDocument[]> {
    try {
      const productCategories = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_CATEGORIES,
        [Query.equal('productId', productId)]
      );

      const categoryIds = productCategories.documents.map((pc: any) => pc.categoryId);
      if (categoryIds.length === 0) return [];

      const categories = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        [Query.equal('$id', categoryIds)]
      );
      return categories.documents as CategoryDocument[];
    } catch (error) {
      console.error('Error fetching product categories:', error);
      return [];
    }
  }

  static async getProductTags(productId: string): Promise<TagDocument[]> {
    try {
      const productTags = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_TAGS,
        [Query.equal('productId', productId)]
      );

      const tagIds = productTags.documents.map((pt: any) => pt.tagId);
      if (tagIds.length === 0) return [];

      const tags = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        [Query.equal('$id', tagIds)]
      );
      return tags.documents as TagDocument[];
    } catch (error) {
      console.error('Error fetching product tags:', error);
      return [];
    }
  }

  static async deleteProductImages(productId: string): Promise<void> {
    try {
      const images = await this.getProductImages(productId);
      await Promise.all(
        images.map(image => 
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, image.$id)
        )
      );
    } catch (error) {
      console.error('Error deleting product images:', error);
    }
  }

  static async deleteProductSpecifications(productId: string): Promise<void> {
    try {
      const specs = await this.getProductSpecifications(productId);
      await Promise.all(
        specs.map(spec => 
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, spec.$id)
        )
      );
    } catch (error) {
      console.error('Error deleting product specifications:', error);
    }
  }

  static async deleteProductRelationships(productId: string): Promise<void> {
    try {
      // Delete product-category relationships
      const productCategories = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_CATEGORIES,
        [Query.equal('productId', productId)]
      );
      await Promise.all(
        productCategories.documents.map(pc => 
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, pc.$id)
        )
      );

      // Delete product-tag relationships
      const productTags = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_TAGS,
        [Query.equal('productId', productId)]
      );
      await Promise.all(
        productTags.documents.map(pt => 
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, pt.$id)
        )
      );
    } catch (error) {
      console.error('Error deleting product relationships:', error);
    }
  }

  static async updateProductCategories(productId: string, categoryIds: string[]): Promise<void> {
    try {
      // Delete existing relationships
      const existingCategories = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_CATEGORIES,
        [Query.equal('productId', productId)]
      );

      await Promise.all(
        existingCategories.documents.map(rel =>
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, rel.$id)
        )
      );

      // Create new relationships
      await Promise.all(
        categoryIds.map(categoryId =>
          databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.PRODUCT_CATEGORIES,
            ID.unique(),
            { productId, categoryId }
          )
        )
      );
    } catch (error) {
      console.error('Error updating product categories:', error);
      throw error;
    }
  }

  static async updateProductTags(productId: string, tagIds: string[]): Promise<void> {
    try {
      // Delete existing relationships
      const existingTags = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_TAGS,
        [Query.equal('productId', productId)]
      );

      await Promise.all(
        existingTags.documents.map(rel =>
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, rel.$id)
        )
      );

      // Create new relationships
      await Promise.all(
        tagIds.map(tagId =>
          databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.PRODUCT_TAGS,
            ID.unique(),
            { productId, tagId }
          )
        )
      );
    } catch (error) {
      console.error('Error updating product tags:', error);
      throw error;
    }
  }

  static async updateProductSpecifications(productId: string, specifications: Array<{key: string; value: any}>): Promise<void> {
    try {
      // Delete existing specifications
      await this.deleteProductSpecifications(productId);

      // Create new specifications
      await Promise.all(
        specifications.map(spec =>
          databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.SPECIFICATIONS,
            ID.unique(),
            {
              productId,
              key: spec.key,
              value: typeof spec.value === 'object' ? JSON.stringify(spec.value) : spec.value
            }
          )
        )
      );
    } catch (error) {
      console.error('Error updating product specifications:', error);
      throw error;
    }
  }
}

// Category operations
export class AppwriteCategoryService {
  static async createCategory(categoryData: CreateCategoryRequest): Promise<CategoryDocument> {
    try {
      const category = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        ID.unique(),
        categoryData
      );
      return category as CategoryDocument;
    } catch (error) {
      console.error('Error creating category:', error);
      throw new Error('Failed to create category');
    }
  }

  static async getCategoryById(categoryId: string): Promise<CategoryDocument | null> {
    try {
      const category = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        categoryId
      );
      return category as CategoryDocument;
    } catch (error) {
      console.error('Error fetching category:', error);
      return null;
    }
  }

  static async getAllCategories(options: QueryOptions = {}): Promise<CategoryDocument[]> {
    try {
      const queries = [];

      if (options.limit) queries.push(Query.limit(options.limit));
      if (options.offset) queries.push(Query.offset(options.offset));
      if (options.orderBy) {
        const direction = options.orderDirection === 'asc' ? Query.orderAsc : Query.orderDesc;
        queries.push(direction(options.orderBy));
      }

      const categories = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        queries
      );
      return categories.documents as CategoryDocument[];
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new Error('Failed to fetch categories');
    }
  }

  static async updateCategory(categoryId: string, categoryData: Partial<CreateCategoryRequest>): Promise<CategoryDocument> {
    try {
      const category = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        categoryId,
        categoryData
      );
      return category as CategoryDocument;
    } catch (error) {
      console.error('Error updating category:', error);
      throw new Error('Failed to update category');
    }
  }

  static async deleteCategory(categoryId: string): Promise<boolean> {
    try {
      // Delete category-product relationships first
      const productCategories = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_CATEGORIES,
        [Query.equal('categoryId', categoryId)]
      );

      await Promise.all(
        productCategories.documents.map(pc =>
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, pc.$id)
        )
      );

      // Delete the category
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        categoryId
      );
      return true;
    } catch (error) {
      console.error('Error deleting category:', error);
      return false;
    }
  }
}

// Tag operations
export class AppwriteTagService {
  static async createTag(tagData: CreateTagRequest): Promise<TagDocument> {
    try {
      const tag = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        ID.unique(),
        tagData
      );
      return tag as TagDocument;
    } catch (error) {
      console.error('Error creating tag:', error);
      throw new Error('Failed to create tag');
    }
  }

  static async getTagById(tagId: string): Promise<TagDocument | null> {
    try {
      const tag = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        tagId
      );
      return tag as TagDocument;
    } catch (error) {
      console.error('Error fetching tag:', error);
      return null;
    }
  }

  static async getAllTags(options: QueryOptions = {}): Promise<TagDocument[]> {
    try {
      const queries = [];

      if (options.limit) queries.push(Query.limit(options.limit));
      if (options.offset) queries.push(Query.offset(options.offset));
      if (options.orderBy) {
        const direction = options.orderDirection === 'asc' ? Query.orderAsc : Query.orderDesc;
        queries.push(direction(options.orderBy));
      }

      const tags = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        queries
      );
      return tags.documents as TagDocument[];
    } catch (error) {
      console.error('Error fetching tags:', error);
      throw new Error('Failed to fetch tags');
    }
  }

  static async updateTag(tagId: string, tagData: Partial<CreateTagRequest>): Promise<TagDocument> {
    try {
      const tag = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        tagId,
        tagData
      );
      return tag as TagDocument;
    } catch (error) {
      console.error('Error updating tag:', error);
      throw new Error('Failed to update tag');
    }
  }

  static async deleteTag(tagId: string): Promise<boolean> {
    try {
      // Delete tag-product relationships first
      const productTags = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_TAGS,
        [Query.equal('tagId', tagId)]
      );

      await Promise.all(
        productTags.documents.map(pt =>
          databases.deleteDocument(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, pt.$id)
        )
      );

      // Delete the tag
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        tagId
      );
      return true;
    } catch (error) {
      console.error('Error deleting tag:', error);
      return false;
    }
  }
}

// Contact Submission operations
export class AppwriteContactService {
  static async createContactSubmission(contactData: CreateContactSubmissionRequest): Promise<ContactSubmissionDocument> {
    try {
      const contact = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        ID.unique(),
        {
          ...contactData,
          status: ContactStatus.UNREAD
        }
      );
      return contact as ContactSubmissionDocument;
    } catch (error) {
      console.error('Error creating contact submission:', error);
      throw new Error('Failed to create contact submission');
    }
  }

  static async getContactSubmissionById(contactId: string): Promise<ContactSubmissionDocument | null> {
    try {
      const contact = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        contactId
      );
      return contact as ContactSubmissionDocument;
    } catch (error) {
      console.error('Error fetching contact submission:', error);
      return null;
    }
  }

  static async getAllContactSubmissions(options: QueryOptions = {}): Promise<ContactSubmissionDocument[]> {
    try {
      const queries = [];

      if (options.limit) queries.push(Query.limit(options.limit));
      if (options.offset) queries.push(Query.offset(options.offset));
      if (options.orderBy) {
        const direction = options.orderDirection === 'asc' ? Query.orderAsc : Query.orderDesc;
        queries.push(direction(options.orderBy));
      }

      const contacts = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        queries
      );
      return contacts.documents as ContactSubmissionDocument[];
    } catch (error) {
      console.error('Error fetching contact submissions:', error);
      throw new Error('Failed to fetch contact submissions');
    }
  }

  static async updateContactSubmission(contactId: string, status: ContactStatus): Promise<ContactSubmissionDocument> {
    try {
      const contact = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        contactId,
        { status }
      );
      return contact as ContactSubmissionDocument;
    } catch (error) {
      console.error('Error updating contact submission:', error);
      throw new Error('Failed to update contact submission');
    }
  }

  static async deleteContactSubmission(contactId: string): Promise<boolean> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        contactId
      );
      return true;
    } catch (error) {
      console.error('Error deleting contact submission:', error);
      return false;
    }
  }
}
