import { hash } from "bcrypt";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/authOptions"; 
import { redirect } from "next/navigation";

export async function hashPassword(password: string): Promise<string> {
  return hash(password, 10);
}

export async function getSession() {
  return await getServerSession(authOptions);
}

export async function getCurrentUser() {
  const session = await getSession();
  return session?.user;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }
  
  return user;
}

export async function requireAdmin() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }
  
  if (user.role !== "ADMIN") {
    redirect("/dashboard");
  }
  
  return user;
}
