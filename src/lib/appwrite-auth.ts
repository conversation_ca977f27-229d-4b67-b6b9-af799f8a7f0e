import { account, databases, DATABASE_ID, COLLECTIONS, Query } from '@/lib/appwrite';
import { AppwriteUserService } from '@/lib/appwrite-server';
import { UserDocument, CreateUserRequest } from '@/types/appwrite';
import { UserRole } from '@/lib/appwrite';
import { hash, compare } from 'bcrypt';

export interface AuthUser {
  $id: string;
  name: string;
  email: string;
  role: UserRole;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role?: UserRole;
}

export class AppwriteAuthService {
  // Create a new user account
  static async register(userData: RegisterData): Promise<AuthUser> {
    try {
      // Hash the password
      const hashedPassword = await hash(userData.password, 10);

      // Create user document in database
      const userDoc = await AppwriteUserService.createUser({
        name: userData.name,
        email: userData.email,
        password: hashedPassword,
        role: userData.role || UserRole.USER
      });

      return {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        role: userDoc.role
      };
    } catch (error) {
      console.error('Error registering user:', error);
      throw new Error('Failed to register user');
    }
  }

  // Login with email and password
  static async login(credentials: LoginCredentials): Promise<AuthUser> {
    try {
      // Find user by email
      const userDoc = await AppwriteUserService.getUserByEmail(credentials.email);
      if (!userDoc) {
        throw new Error('Invalid credentials');
      }

      // Verify password
      const isPasswordValid = await compare(credentials.password, userDoc.password);
      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      return {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        role: userDoc.role
      };
    } catch (error) {
      console.error('Error logging in user:', error);
      throw new Error('Invalid credentials');
    }
  }

  // Get current user from session
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      // In a real implementation, this would check the session/JWT token
      // For now, we'll implement a simple session check
      const session = await this.getSession();
      if (!session) return null;

      const userDoc = await AppwriteUserService.getUserById(session.userId);
      if (!userDoc) return null;

      return {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        role: userDoc.role
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Create session (simplified implementation)
  static async createSession(user: AuthUser): Promise<string> {
    try {
      // In a real implementation, this would create a JWT token or use Appwrite's session management
      // For now, we'll use a simple approach
      const sessionData = {
        userId: user.$id,
        email: user.email,
        role: user.role,
        createdAt: new Date().toISOString()
      };

      // Store session in localStorage (client-side) or cookies (server-side)
      if (typeof window !== 'undefined') {
        localStorage.setItem('appwrite_session', JSON.stringify(sessionData));
      }

      return JSON.stringify(sessionData);
    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error('Failed to create session');
    }
  }

  // Get session
  static async getSession(): Promise<{ userId: string; email: string; role: UserRole } | null> {
    try {
      if (typeof window !== 'undefined') {
        const sessionData = localStorage.getItem('appwrite_session');
        if (sessionData) {
          return JSON.parse(sessionData);
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  // Logout
  static async logout(): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('appwrite_session');
      }
    } catch (error) {
      console.error('Error logging out:', error);
    }
  }

  // Check if user is authenticated
  static async isAuthenticated(): Promise<boolean> {
    const session = await this.getSession();
    return session !== null;
  }

  // Check if user has admin role
  static async isAdmin(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user?.role === UserRole.ADMIN;
  }

  // Update user password
  static async updatePassword(userId: string, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const userDoc = await AppwriteUserService.getUserById(userId);
      if (!userDoc) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await compare(currentPassword, userDoc.password);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await hash(newPassword, 10);

      // Update user password
      await AppwriteUserService.updateUser(userId, { password: hashedNewPassword });
      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      return false;
    }
  }

  // Require authentication (middleware helper)
  static async requireAuth(): Promise<AuthUser> {
    const user = await this.getCurrentUser();
    if (!user) {
      throw new Error('Authentication required');
    }
    return user;
  }

  // Require admin role (middleware helper)
  static async requireAdmin(): Promise<AuthUser> {
    const user = await this.requireAuth();
    if (user.role !== UserRole.ADMIN) {
      throw new Error('Admin access required');
    }
    return user;
  }
}

// Server-side authentication helpers
export class AppwriteServerAuth {
  // Get user from request headers (for API routes)
  static async getUserFromRequest(request: Request): Promise<AuthUser | null> {
    try {
      // In a real implementation, this would extract and verify JWT from Authorization header
      // For now, we'll use a simplified approach
      const authHeader = request.headers.get('Authorization');
      if (!authHeader) return null;

      const token = authHeader.replace('Bearer ', '');
      const sessionData = JSON.parse(token);
      
      const userDoc = await AppwriteUserService.getUserById(sessionData.userId);
      if (!userDoc) return null;

      return {
        $id: userDoc.$id,
        name: userDoc.name,
        email: userDoc.email,
        role: userDoc.role
      };
    } catch (error) {
      console.error('Error getting user from request:', error);
      return null;
    }
  }

  // Middleware for protecting API routes
  static async protectRoute(request: Request): Promise<AuthUser> {
    const user = await this.getUserFromRequest(request);
    if (!user) {
      throw new Error('Unauthorized');
    }
    return user;
  }

  // Middleware for admin-only routes
  static async protectAdminRoute(request: Request): Promise<AuthUser> {
    const user = await this.protectRoute(request);
    if (user.role !== UserRole.ADMIN) {
      throw new Error('Admin access required');
    }
    return user;
  }
}

// Hash password utility
export async function hashPassword(password: string): Promise<string> {
  return hash(password, 10);
}

// Compare password utility
export async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword);
}
