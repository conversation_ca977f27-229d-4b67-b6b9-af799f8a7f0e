import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Interface for storage providers
export interface StorageProvider {
  uploadFile(file: Buffer, fileName: string, mimeType: string): Promise<string>;
  deleteFile(fileUrl: string): Promise<boolean>;
}

// Local file system storage provider (for development)
export class LocalStorageProvider implements StorageProvider {
  private uploadDir: string;
  private publicPath: string;

  constructor() {
    this.uploadDir = path.join(process.cwd(), 'public/uploads');
    this.publicPath = '/uploads';
  }

  async uploadFile(file: Buffer, fileName: string): Promise<string> {
    try {
      // Ensure upload directory exists
      await mkdir(this.uploadDir, { recursive: true });
      
      // Generate unique filename
      const fileExtension = fileName.split('.').pop() || '';
      const uniqueFileName = `${uuidv4()}.${fileExtension}`;
      const filePath = path.join(this.uploadDir, uniqueFileName);
      
      // Write file to disk
      await writeFile(filePath, file);
      
      // Return public URL
      return `${this.publicPath}/${uniqueFileName}`;
    } catch (error) {
      console.error('Error uploading file to local storage:', error);
      throw new Error('Failed to upload file to local storage');
    }
  }

  async deleteFile(fileUrl: string): Promise<boolean> {
    try {
      // Extract filename from URL
      const fileName = fileUrl.split('/').pop();
      if (!fileName) return false;
      
      // Delete file
      const filePath = path.join(this.uploadDir, fileName);
      await writeFile(filePath, ''); // Overwrite with empty file (safer than deleting)
      
      return true;
    } catch (error) {
      console.error('Error deleting file from local storage:', error);
      return false;
    }
  }
}

// Cloud storage provider (for production)
// This is a placeholder implementation that would be replaced with actual cloud storage SDK
export class CloudStorageProvider implements StorageProvider {
  private bucketName: string;
  private region: string;

  constructor(bucketName: string, region: string = 'us-east-1') {
    this.bucketName = bucketName;
    this.region = region;
  }

  async uploadFile(file: Buffer, fileName: string, mimeType: string): Promise<string> {
    try {
      // In a real implementation, this would use AWS SDK, Google Cloud Storage, etc.
      // For now, we'll just log and use the local storage as a fallback
      console.log(`[CLOUD STORAGE] Would upload file ${fileName} to bucket ${this.bucketName}`);
      
      // Fallback to local storage for development
      const localProvider = new LocalStorageProvider();
      return await localProvider.uploadFile(file, fileName, mimeType);
    } catch (error) {
      console.error('Error uploading file to cloud storage:', error);
      throw new Error('Failed to upload file to cloud storage');
    }
  }

  async deleteFile(fileUrl: string): Promise<boolean> {
    try {
      // In a real implementation, this would use AWS SDK, Google Cloud Storage, etc.
      console.log(`[CLOUD STORAGE] Would delete file ${fileUrl} from bucket ${this.bucketName}`);
      
      // Fallback to local storage for development
      const localProvider = new LocalStorageProvider();
      return await localProvider.deleteFile(fileUrl);
    } catch (error) {
      console.error('Error deleting file from cloud storage:', error);
      return false;
    }
  }
}

// Factory function to get the appropriate storage provider based on environment
export function getStorageProvider(): StorageProvider {
  if (process.env.NODE_ENV === 'production' && process.env.STORAGE_BUCKET) {
    return new CloudStorageProvider(
      process.env.STORAGE_BUCKET,
      process.env.STORAGE_REGION
    );
  }
  
  return new LocalStorageProvider();
}

// Helper function to upload a file
export async function uploadFile(file: Buffer, fileName: string, mimeType: string): Promise<string> {
  const provider = getStorageProvider();
  return await provider.uploadFile(file, fileName, mimeType);
}

// Helper function to delete a file
export async function deleteFile(fileUrl: string): Promise<boolean> {
  const provider = getStorageProvider();
  return await provider.deleteFile(fileUrl);
}
