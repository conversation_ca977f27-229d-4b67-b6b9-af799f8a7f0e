import { Client, Account, Databases, Storage, Query, ID, Permission, Role } from 'appwrite';

// Appwrite configuration
const client = new Client();

client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '');

// Initialize Appwrite services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

// Database and collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '';
export const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || '',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || '',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || '',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || '',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || '',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || '',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || '',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || '',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || '',
};

// Storage bucket ID
export const STORAGE_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID || '';

// Export client for custom configurations
export { client, Query, ID, Permission, Role };

// Helper function to create server-side client with API key
export function createServerClient() {
  const serverClient = new Client();
  
  serverClient
    .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
    .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
    .setKey(process.env.APPWRITE_API_KEY || '');

  return {
    client: serverClient,
    account: new Account(serverClient),
    databases: new Databases(serverClient),
    storage: new Storage(serverClient),
  };
}

// Types for Appwrite documents
export interface AppwriteDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
}

// User roles enum
export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN'
}

// Contact status enum
export enum ContactStatus {
  UNREAD = 'UNREAD',
  READ = 'READ',
  REPLIED = 'REPLIED',
  ARCHIVED = 'ARCHIVED'
}
