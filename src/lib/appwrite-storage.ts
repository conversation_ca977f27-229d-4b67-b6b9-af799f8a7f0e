import { storage, STORAGE_BUCKET_ID, ID, Permission, Role } from '@/lib/appwrite';
import { createServerClient } from '@/lib/appwrite';
import { FileUploadResponse } from '@/types/appwrite';

export interface UploadFileOptions {
  permissions?: string[];
  onProgress?: (progress: { $id: string; progress: number; sizeUploaded: number; chunksTotal: number; chunksUploaded: number }) => void;
}

export class AppwriteStorageService {
  // Upload a file to Appwrite Storage
  static async uploadFile(
    file: File | Buffer, 
    fileName?: string, 
    options: UploadFileOptions = {}
  ): Promise<FileUploadResponse> {
    try {
      const fileId = ID.unique();
      const actualFileName = fileName || (file instanceof File ? file.name : `file-${Date.now()}`);
      
      // Set default permissions (public read, authenticated write)
      const permissions = options.permissions || [
        Permission.read(Role.any()),
        Permission.write(Role.users()),
        Permission.delete(Role.users())
      ];

      let uploadFile: File;
      
      // Convert Buffer to File if necessary
      if (file instanceof Buffer) {
        const blob = new Blob([file]);
        uploadFile = new File([blob], actualFileName);
      } else {
        uploadFile = file;
      }

      const response = await storage.createFile(
        STORAGE_BUCKET_ID,
        fileId,
        uploadFile,
        permissions,
        options.onProgress
      );

      return response as FileUploadResponse;
    } catch (error) {
      console.error('Error uploading file to Appwrite Storage:', error);
      throw new Error('Failed to upload file');
    }
  }

  // Get file URL
  static getFileUrl(fileId: string): string {
    try {
      return storage.getFileView(STORAGE_BUCKET_ID, fileId).toString();
    } catch (error) {
      console.error('Error getting file URL:', error);
      throw new Error('Failed to get file URL');
    }
  }

  // Get file download URL
  static getFileDownloadUrl(fileId: string): string {
    try {
      return storage.getFileDownload(STORAGE_BUCKET_ID, fileId).toString();
    } catch (error) {
      console.error('Error getting file download URL:', error);
      throw new Error('Failed to get file download URL');
    }
  }

  // Get file preview URL (for images)
  static getFilePreviewUrl(
    fileId: string, 
    width?: number, 
    height?: number, 
    gravity?: 'center' | 'top-left' | 'top' | 'top-right' | 'left' | 'right' | 'bottom-left' | 'bottom' | 'bottom-right',
    quality?: number,
    borderWidth?: number,
    borderColor?: string,
    borderRadius?: number,
    opacity?: number,
    rotation?: number,
    background?: string,
    output?: 'jpg' | 'jpeg' | 'png' | 'gif' | 'webp'
  ): string {
    try {
      return storage.getFilePreview(
        STORAGE_BUCKET_ID,
        fileId,
        width,
        height,
        gravity,
        quality,
        borderWidth,
        borderColor,
        borderRadius,
        opacity,
        rotation,
        background,
        output
      ).toString();
    } catch (error) {
      console.error('Error getting file preview URL:', error);
      throw new Error('Failed to get file preview URL');
    }
  }

  // Delete a file
  static async deleteFile(fileId: string): Promise<boolean> {
    try {
      await storage.deleteFile(STORAGE_BUCKET_ID, fileId);
      return true;
    } catch (error) {
      console.error('Error deleting file from Appwrite Storage:', error);
      return false;
    }
  }

  // Get file information
  static async getFileInfo(fileId: string) {
    try {
      return await storage.getFile(STORAGE_BUCKET_ID, fileId);
    } catch (error) {
      console.error('Error getting file info:', error);
      throw new Error('Failed to get file information');
    }
  }

  // List files in bucket
  static async listFiles(search?: string, limit?: number, offset?: number) {
    try {
      const queries = [];
      if (search) queries.push(`search("${search}")`);
      if (limit) queries.push(`limit(${limit})`);
      if (offset) queries.push(`offset(${offset})`);

      return await storage.listFiles(STORAGE_BUCKET_ID, queries);
    } catch (error) {
      console.error('Error listing files:', error);
      throw new Error('Failed to list files');
    }
  }

  // Update file permissions
  static async updateFilePermissions(fileId: string, permissions: string[]) {
    try {
      return await storage.updateFile(STORAGE_BUCKET_ID, fileId, undefined, permissions);
    } catch (error) {
      console.error('Error updating file permissions:', error);
      throw new Error('Failed to update file permissions');
    }
  }

  // Validate file type
  static validateFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type);
  }

  // Validate file size
  static validateFileSize(file: File, maxSizeInMB: number): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }

  // Get optimized image URL with default settings
  static getOptimizedImageUrl(
    fileId: string,
    width: number = 800,
    height: number = 600,
    quality: number = 80
  ): string {
    return this.getFilePreviewUrl(
      fileId,
      width,
      height,
      'center',
      quality,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      'webp'
    );
  }
}

// Server-side storage service (for API routes)
export class AppwriteServerStorageService {
  private static serverStorage = createServerClient().storage;

  // Upload file from server
  static async uploadFileFromServer(
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string,
    permissions?: string[]
  ): Promise<FileUploadResponse> {
    try {
      const fileId = ID.unique();
      
      // Convert Buffer to File
      const blob = new Blob([fileBuffer], { type: mimeType });
      const file = new File([blob], fileName, { type: mimeType });

      const defaultPermissions = permissions || [
        Permission.read(Role.any()),
        Permission.write(Role.users()),
        Permission.delete(Role.users())
      ];

      const response = await this.serverStorage.createFile(
        STORAGE_BUCKET_ID,
        fileId,
        file,
        defaultPermissions
      );

      return response as FileUploadResponse;
    } catch (error) {
      console.error('Error uploading file from server:', error);
      throw new Error('Failed to upload file from server');
    }
  }

  // Delete file from server
  static async deleteFileFromServer(fileId: string): Promise<boolean> {
    try {
      await this.serverStorage.deleteFile(STORAGE_BUCKET_ID, fileId);
      return true;
    } catch (error) {
      console.error('Error deleting file from server:', error);
      return false;
    }
  }

  // Get file info from server
  static async getFileInfoFromServer(fileId: string) {
    try {
      return await this.serverStorage.getFile(STORAGE_BUCKET_ID, fileId);
    } catch (error) {
      console.error('Error getting file info from server:', error);
      throw new Error('Failed to get file information from server');
    }
  }
}

// Helper functions for common file operations
export const FileHelpers = {
  // Extract file ID from Appwrite file URL
  extractFileIdFromUrl: (url: string): string | null => {
    try {
      const urlParts = url.split('/');
      const fileIdIndex = urlParts.findIndex(part => part === 'files') + 1;
      return urlParts[fileIdIndex] || null;
    } catch (error) {
      console.error('Error extracting file ID from URL:', error);
      return null;
    }
  },

  // Generate thumbnail URL
  generateThumbnailUrl: (fileId: string): string => {
    return AppwriteStorageService.getFilePreviewUrl(fileId, 300, 300, 'center', 70);
  },

  // Generate different image sizes
  generateImageSizes: (fileId: string) => ({
    thumbnail: AppwriteStorageService.getFilePreviewUrl(fileId, 300, 300, 'center', 70),
    small: AppwriteStorageService.getFilePreviewUrl(fileId, 600, 400, 'center', 80),
    medium: AppwriteStorageService.getFilePreviewUrl(fileId, 1200, 800, 'center', 85),
    large: AppwriteStorageService.getFilePreviewUrl(fileId, 1920, 1080, 'center', 90),
    original: AppwriteStorageService.getFileUrl(fileId)
  }),

  // Check if file is an image
  isImage: (mimeType: string): boolean => {
    return mimeType.startsWith('image/');
  },

  // Get file extension from mime type
  getFileExtension: (mimeType: string): string => {
    const extensions: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',
      'image/svg+xml': 'svg',
      'application/pdf': 'pdf',
      'text/plain': 'txt',
      'application/json': 'json'
    };
    return extensions[mimeType] || 'bin';
  }
};
