'use client'

import React from 'react';
import Header from '@/components/organisms/Header';
import Footer from '@/components/organisms/Footer';
import SkipLink from '@/components/accessibility/SkipLink';
import AOS from "aos";
import "aos/dist/aos.css";

interface MainLayoutProps {
  children: React.ReactNode;
}


 
/**
 * MainLayout component that wraps all pages with a consistent header and footer
 *
 * @param {React.ReactNode} children - The page content to be wrapped
 * @returns {React.ReactNode} The layout with header, content, and footer
 */
export default function MainLayout({ children }: MainLayoutProps): React.ReactNode {
  // Initialize AOS
  React.useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: 'ease-out-cubic',
      once: true,
      offset: 100,
      delay: 0,
    });
    
    // Refresh AOS on route change or component update
    AOS.refresh();

    // Cleanup
    return () => {
      AOS.refresh();
    };
  }, []);
  return (
    <div className="flex flex-col min-h-screen">
      {/* Skip Links for Accessibility */}
      <SkipLink href="#main-content">Skip to main content</SkipLink>
      <SkipLink href="#navigation">Skip to navigation</SkipLink>

      <Header />
      <main id="main-content" className="flex-grow" tabIndex={-1}>
        {children}
      </main>
      <Footer />
    </div>
  );
}
