import React from 'react'
import Image from "next/image";
import { highlights } from "@/store";

export default function CompanyHighlights() {
  return (
      <section className="relative">
      {/* Background Image */}
      <div className="relative w-full h-20 sm:h-32 lg:h-[25rem] overflow-hidden">
        <Image
          src="/assets/boat done.png"
          alt="Background Image"
          fill
          className="absolute inset-0 object-cover"
        />
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/30 via-black/20 to-black/90 z-0 pointer-events-none" />

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 px-4 mt-[-50px] sm:mt-[-70px] lg:mt-[-300px]">
        {highlights.map(({ title, description, icon: Icon, duration }) => (
          <div
            key={title}
            className="flex flex-col items-center text-center gap-3"
            data-aos="fade-up"
            data-aos-delay="200"
            data-aos-duration={duration}
          >
            {/* Icon */}
            <div className="text-sun-gold">
              <Icon size={66} />
            </div>

            {/* Text */}
            <div>
              <h5 className="text-[18px] font-baru-semibold text-white">
                {title}
              </h5>
              <p className="text-[10px] font-baru-regular text-gray-100 leading-snug">
                {description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}
