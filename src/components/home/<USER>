import React from 'react'
import Image from "next/image";
import Link from "next/link";

export default function WhySection(data) {
  return (
     <section
      className={`bg-white text-deep-charcoal w-full overflow-hidden pl-5 py-8 sm:py-12 lg:py-16`}
      data-aos="fade-up"
      data-aos-duration="1000"
    >
      <div className="max-w-7xl mx-auto">
        {/* Title */}
        <h2
          className="text-2xl sm:text-3xl lg:text-4xl font-baru-bold text-earthy-cocoa mb-6 sm:mb-8 lg:mb-12 text-center"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          {data.title}
        </h2>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-12">
          {/* Features Section */}
          <div className="flex-1 flex flex-col justify-center space-y-4 sm:space-y-6 font-baru-regularbold">
            {data.features.map((item, index) => (
              <div
                key={index}
                className="border-l-4 border-sun-gold pl-4 sm:pl-6"
                data-aos="fade-right"
                data-aos-delay={400 + index * 200}
                data-aos-duration="700"
              >
                <h6 className="text-lg sm:text-xl font-baru-semibold mb-2 sm:mb-3 text-earthy-cocoa">
                  {item.title}
                </h6>
                <p className="text-sm sm:text-base font-baru-regular text-deep-charcoal leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}

            {/* CTA Button */}
            {data.ctaButton && (
              <Link href={data.ctaButton.href}>
                <button
                  aria-label={`CTA: ${data.ctaButton.text}`}
                  className="transition-all duration-300 transform hover:scale-105 w-fit mt-4 sm:mt-6 bg-earthy-cocoa hover:bg-earthy-cocoa text-white font-baru-regularbold py-2 sm:py-3 px-6 sm:px-8 rounded-lg text-sm sm:text-base"
                  data-aos="fade-up"
                  data-aos-delay="1000"
                >
                  {data.ctaButton.text}
                </button>
              </Link>
            )}
          </div>

          {/* Image Section */}
          <div
            data-aos="fade-left"
            data-aos-delay="300"
            data-aos-duration="1000"
            className="relative w-full h-72 sm:h-80 lg:h-[32.5rem] lg:w-[50%] rounded-l-full overflow-hidden"
          >
            <Image
              className="object-cover"
              src={data.image.src}
              alt={data.image.alt}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
