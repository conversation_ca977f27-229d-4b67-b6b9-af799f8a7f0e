import React from 'react'
import Image from "next/image";
import Link from "next/link";

export default function PartnershipSection(data) {
  return (
     <section
      className={`bg-white pr-4 lg:pr-8 py-8 sm:py-12 lg:py-16 relative overflow-hidden `}
      data-aos="fade-up"
      data-aos-duration="1000"
    >
      <div className="max-w-7xl mx-auto ">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-12">
          <div
            data-aos="fade-right"
            data-aos-delay="200"
            data-aos-duration="1000"
            className="relative w-full h-64 sm:h-80 lg:h-130 lg:w-[50%] rounded-r-full overflow-hidden shadow-none"
          >
            <Image
              className="w-full h-full object-cover shadow-none"
              src={data.image.src}
              alt={data.image.alt}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center space-y-4 sm:space-y-6">
            <div
              data-aos="fade-left"
              data-aos-delay="400"
              data-aos-duration="800"
            >
              <h6 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4 text-[#5D4037]">
                {data.title}
              </h6>
              {data.paragraphs.map((paragraph, index) => (
                <p
                  key={index}
                  className="text-sm sm:text-base text-gray-600 leading-relaxed mb-3 sm:mb-4"
                  data-aos="fade-up"
                  data-aos-delay={600 + index * 100}
                  data-aos-duration="600"
                >
                  {paragraph}
                </p>
              ))}
            </div>
            {data.ctaButton && (
              <Link href={data.ctaButton.href}>
                <button
                  className="transition-all duration-300 transform hover:scale-105 w-fit mt-4 sm:mt-6 bg-[#5D4037] hover:bg-[#5D4037] text-[white] font-semibold py-2 sm:py-3 px-6 sm:px-8 rounded-lg shadow-none text-sm sm:text-base"
                  data-aos="fade-up"
                  data-aos-delay="1000"
                >
                  {data.ctaButton.text}
                </button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}
