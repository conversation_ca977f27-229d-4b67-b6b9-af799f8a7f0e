"use client";
import Image from 'next/image';
import React, { useEffect, useState } from "react";

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

 const products = [
  { name: "COCO<PERSON>", image: "🍫" },
  { name: "CASHEW NUT", image: "🌰" },
  { name: "COCONUT", image: "🥥" },
  { name: "PINEAPPLE", image: "🍍" },
  { name: "<PERSON>NG<PERSON>", image: "🥭" },
  { name: "GINGER", image: "🫚" },
];


  const productsPerView = 4;
  const totalSlides = Math.ceil(products.length / productsPerView);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 1500);

    return () => clearInterval(timer);
  }, [totalSlides]);

  const getCurrentProducts = () => {
    const startIndex = currentSlide * productsPerView;
    return products.slice(startIndex, startIndex + productsPerView);
  };

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden bg-[forest-green] pt-20">
      {/* <div className="absolute right-0 top-0 h-full w-1/2">
        <div className="w-full h-full bg-[#121212] rounded-l-full" 
          data-aos="fade-left"
            data-aos-delay="200"/>
      </div> */}

      {/* Background decorative fruits */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Right side fruits */}
        <div
          className="absolute top-20 right-10 animate-float"
          style={{ animationDelay: "0s" }}
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            className="opacity-20"
          >
            <circle cx="20" cy="22" r="15" fill="#FF6B6B" />
            <ellipse cx="20" cy="20" rx="12" ry="15" fill="#FF5252" />
            <rect x="19" y="8" width="2" height="8" fill="#4CAF50" />
            <ellipse cx="22" cy="10" rx="3" ry="2" fill="#4CAF50" />
          </svg>
        </div>

        {/* Orange */}
        <div
          className="absolute top-40 right-32 animate-float"
          style={{ animationDelay: "1s" }}
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            className="opacity-15"
          >
            <circle cx="16" cy="16" r="12" fill="#FF9800" />
            <circle cx="16" cy="16" r="10" fill="#FFB74D" />
            <circle cx="13" cy="13" r="1" fill="#FF8F00" opacity="0.5" />
            <circle cx="19" cy="12" r="1" fill="#FF8F00" opacity="0.5" />
            <circle cx="18" cy="18" r="1" fill="#FF8F00" opacity="0.5" />
          </svg>
        </div>

        {/* Banana */}
        <div
          className="absolute bottom-32 right-20 animate-float"
          style={{ animationDelay: "2s" }}
        >
          <svg
            width="50"
            height="50"
            viewBox="0 0 50 50"
            className="opacity-10"
          >
            <path
              d="M15 35 Q10 25 12 15 Q15 10 20 12 Q35 15 40 25 Q35 35 25 32 Q20 30 15 35Z"
              fill="#FFEB3B"
            />
            <path
              d="M17 33 Q12 25 14 17 Q16 13 20 14 Q32 16 36 24 Q32 32 24 30 Q20 28 17 33Z"
              fill="#FFF176"
            />
          </svg>
        </div>

        {/* Strawberry */}
        <div
          className="absolute top-60 right-60 animate-float"
          style={{ animationDelay: "0.5s" }}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            className="opacity-20"
          >
            <path
              d="M12 20 L8 12 Q8 8 12 6 Q16 8 16 12 L12 20Z"
              fill="#F44336"
            />
            <ellipse cx="12" cy="8" rx="4" ry="2" fill="#4CAF50" />
            <circle cx="10" cy="12" r="0.5" fill="#FFE0E0" />
            <circle cx="14" cy="14" r="0.5" fill="#FFE0E0" />
            <circle cx="12" cy="16" r="0.5" fill="#FFE0E0" />
          </svg>
        </div>

        {/* Grapes */}
        <div
          className="absolute bottom-60 right-40 animate-float"
          style={{ animationDelay: "1.5s" }}
        >
          <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            className="opacity-15"
          >
            <circle cx="15" cy="8" r="3" fill="#9C27B0" />
            <circle cx="12" cy="12" r="3" fill="#9C27B0" />
            <circle cx="18" cy="12" r="3" fill="#9C27B0" />
            <circle cx="15" cy="16" r="3" fill="#9C27B0" />
            <circle cx="12" cy="20" r="3" fill="#9C27B0" />
            <circle cx="18" cy="20" r="3" fill="#9C27B0" />
            <circle cx="15" cy="24" r="3" fill="#9C27B0" />
            <path
              d="M15 5 Q12 2 10 4 Q12 6 15 5 Q18 6 20 4 Q18 2 15 5Z"
              fill="#4CAF50"
            />
          </svg>
        </div>

        {/* Pear */}
        <div
          className="absolute top-32 right-24 animate-float"
          style={{ animationDelay: "3s" }}
        >
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            className="opacity-25"
          >
            <ellipse cx="14" cy="20" rx="8" ry="6" fill="#8BC34A" />
            <ellipse cx="14" cy="12" rx="5" ry="8" fill="#AED581" />
            <rect x="13" y="6" width="2" height="4" fill="#5D4037" />
          </svg>
        </div>

        {/* Cherry */}
        <div
          className="absolute bottom-40 right-16 animate-float"
          style={{ animationDelay: "2.5s" }}
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            className="opacity-20"
          >
            <circle cx="12" cy="20" r="6" fill="#F44336" />
            <circle cx="20" cy="18" r="6" fill="#F44336" />
            <path
              d="M12 14 Q8 8 10 4"
              stroke="#4CAF50"
              strokeWidth="2"
              fill="none"
            />
            <path
              d="M20 12 Q24 6 22 2"
              stroke="#4CAF50"
              strokeWidth="2"
              fill="none"
            />
          </svg>
        </div>

        {/* Left side fruits */}
        <div
          className="absolute top-[-100] left-[-190] "
          style={{ animationDelay: "0s" }}
        >
         <Image
  src="/assets/vegetables-7357585_640.png"
  alt="Decorative Vegetables"
  width={500}
  height={500}
  className="opacity-15"
  style={{ objectFit: 'contain' }}
  priority
/>

          {/* <div className="w-[500px] h-[500px] bg-gradient-to-br from-green-200 to-yellow-200 opacity-20 rounded-full transform rotate-12"></div> */}
        </div>

        <div
          className="absolute top-16 left-12 animate-float"
          style={{ animationDelay: "0.3s" }}
        >
          <svg
            width="45"
            height="45"
            viewBox="0 0 45 45"
            className="opacity-15"
          >
            <ellipse cx="22.5" cy="25" rx="18" ry="15" fill="#4CAF50" />
            <ellipse cx="22.5" cy="25" rx="15" ry="12" fill="#FF5252" />
            <ellipse cx="22.5" cy="25" rx="12" ry="9" fill="#FFB3BA" />
            <circle cx="18" cy="22" r="1.5" fill="#2E7D32" />
            <circle cx="27" cy="20" r="1.5" fill="#2E7D32" />
            <circle cx="22" cy="28" r="1.5" fill="#2E7D32" />
          </svg>
        </div>

        {/* Other decorative fruits continue... */}
        <div
          className="absolute bottom-28 left-16 animate-float"
          style={{ animationDelay: "2.8s" }}
        >
          <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            className="opacity-12"
          >
            <ellipse cx="15" cy="15" rx="10" ry="12" fill="#FFEB3B" />
            <ellipse cx="15" cy="15" rx="8" ry="10" fill="#FFF176" />
            <circle cx="12" cy="12" r="0.5" fill="#F57F17" opacity="0.6" />
            <circle cx="18" cy="14" r="0.5" fill="#F57F17" opacity="0.6" />
            <circle cx="15" cy="18" r="0.5" fill="#F57F17" opacity="0.6" />
          </svg>
        </div>
      </div>

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8" data-aos="fade-right">
            {/* Enhanced Product Slider - 4 products per view */}
            <div className="mb-6" data-aos="fade-down" data-aos-delay="100">
              <div className="w-full overflow-hidden relative bg-gradient-to-r from-green-50 to-yellow-50 rounded-2xl p-4 border border-green-200">
                <div className="grid grid-cols-4 gap-4">
                  {getCurrentProducts().map((product, index) => (
                    <div
                      key={`${currentSlide}-${index}`}
                      className="flex flex-col items-center justify-center p-3 bg-white rounded-xl shadow-sm border border-green-100 transform transition-all duration-500 hover:scale-105 hover:shadow-md"
                    >
                      <div className="text-3xl mb-2">{product.image}</div>
                      <div className="text-xs font-bold text-forest-green uppercase tracking-wider text-center">
                        {product.name}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Progress indicators */}
              <div className="flex justify-center space-x-2 mt-4">
                {Array.from({ length: totalSlides }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentSlide
                        ? "bg-forest-green scale-110"
                        : "bg-gray-300 hover:bg-gray-400"
                    }`}
                  />
                ))}
              </div>

              {/* Moving progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-1 mt-3 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-[#5BD05F] to-[#4CAF50] h-1 rounded-full transition-all duration-300 ease-out"
                  style={{
                    width: `${((currentSlide + 1) / totalSlides) * 100}%`,
                  }}
                />
              </div>
            </div>

            <div className="text-5xl font-bold text-transparent bg-gradient-to-b from-[#5BD05F] to-[#4CAF50] bg-clip-text">
              Your Gateway to Global Commodities: Quality, Reliability, and
              Excellence
            </div>

            <div
              className="flex flex-col sm:flex-row gap-4"
              data-aos="fade-right"
              data-aos-delay="400"
            >
              <button className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                Shop Now
              </button>
              <button className="border-2 border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300">
                Learn More
              </button>
            </div>
          </div>

          {/* Right Content - Enhanced Layout */}
          <div
            className="relative flex flex-col items-center justify-center space-y-2"
            data-aos="fade-left"
            data-aos-delay="300"
          >
            {/* Image Container with enhanced styling */}
            <div className="relative group">
              <div
                className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"
                style={{ width: "100%", height: "100%" }}
              ></div>
             <div className="relative w-[400px] h-[400px] sm:w-[400px] sm:h-[400px]">
  {/* Mango - centerpiece */}
  <div className="absolute inset-0 m-auto w-[400px] h-[400px] z-20">
    <Image
      src="/assets/delicious-mango-still-life-removebg-preview.png"
      alt="Mango"
      width={400}
      height={400}
      style={{ objectFit: 'contain' }}
      className="transition-transform duration-500 hover:scale-105"
      priority
    />
  </div>

  {/* Coconut - top left */}
  <div className="absolute top-0 left-0 w-24 h-24 z-10">
    <Image
      src="/assets/fresh-coconut-mellow-delicious-perfect-cut-isolated-white.png"
      alt="Coconut"
      width={96}
      height={96}
      style={{ objectFit: 'contain' }}
      className="transition-transform duration-500 hover:scale-105"
      priority
    />
  </div>

  {/* Cashew - bottom left */}
  <div className="absolute bottom-4 left-2 w-20 h-20 z-10">
    <Image
      src="/assets/fried-cashew.png"
      alt="Cashew"
      width={80}
      height={80}
      style={{ objectFit: 'contain' }}
      className="transition-transform duration-500 hover:scale-105"
      priority
    />
  </div>

  {/* Peanuts - bottom right */}
  <div className="absolute bottom-2 right-0 w-20 h-20 z-10">
    <Image
      src="/assets/pile-roasted-peanuts-ready-eat.png"
      alt="Peanuts"
      width={80}
      height={80}
      style={{ objectFit: 'contain' }}
      className="transition-transform duration-500 hover:scale-105"
      priority
    />
  </div>

  {/* Ginger - bottom mid-right */}
  <div className="absolute bottom-0 right-6 w-24 h-24 z-10">
    <Image
      src="/assets/fresh-ginger-root-culinary-medicinal-delight.png"
      alt="Ginger"
      width={96}
      height={96}
      style={{ objectFit: 'contain' }}
      className="transition-transform duration-500 hover:scale-105"
      priority
    />
  </div>

  {/* Bunch of Nuts - center bottom */}
  <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-20 h-20 z-10">
    <Image
      src="/assets/bunch-nuts-bag-nuts.png"
      alt="Bunch of Nuts"
      width={80}
      height={80}
      style={{ objectFit: 'contain' }}
      className="transition-transform duration-500 hover:scale-105"
      priority
    />
  </div>
</div>


            </div>

            {/* Enhanced text content */}
            <div
              className="text-center max-w-lg space-y-4"
              data-aos="fade-up"
              data-aos-delay="500"
            >
              <p
                className="text-xl lg:text-2xl leading-relaxed font-medium text-left bg-clip-text text-transparent"
                style={{
                  backgroundImage: "linear-gradient(90deg, #FFCA00, #4CAF50)",
                }}
              >
                Welcome to AfricSource, your trusted partner in premium
                agricultural exports. We bridge the gap between local farmers
                and global markets, delivering excellence in every shipment.
              </p>

              {/* Additional feature highlights */}
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }

        .animate-float {
          animation: float 3s ease-in-out infinite;
        }

        .aos-animate {
          opacity: 1;
          transform: translateX(0) translateY(0);
          transition: all 0.8s ease;
        }

        [data-aos="fade-right"] {
          opacity: 0;
          transform: translateX(-50px);
        }

        [data-aos="fade-left"] {
          opacity: 0;
          transform: translateX(50px);
        }

        [data-aos="fade-up"] {
          opacity: 0;
          transform: translateY(50px);
        }

        [data-aos="fade-down"] {
          opacity: 0;
          transform: translateY(-50px);
        }
      `}</style>
    </section>
  );
};

export default HeroSection;
