"use client";

import React, { useEffect, useState } from "react";
import axios from "axios";
import { Star } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

const FeaturedProducts = () => {
  const [products, setProducts] = useState([]);
  const [erroredImages, setErroredImages] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const res = await axios.get("/api/products");
        setProducts(res.data);
      } catch (error) {
        console.error("Error fetching featured products:", error);
      }
    };
    fetchProducts();
  }, []);

  const renderStars = () =>
    Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
    ));

  const handleImageError = (id: string) => {
    setErroredImages((prev) => ({ ...prev, [id]: true }));
  };

  return (
    <div className="bg-white py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16" data-aos="fade-up">
          <h2 className="text-2xl font-light text-forest-green tracking-[0.3em] mb-4">
            NEW FEATURED PRODUCTS
          </h2>
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/assets/section-title-icon-1.webp"
              alt="divider"
              width={50}
              height={50}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {products.slice(0, 4).map((product, index) => {
            const image = product.images?.[0]?.url || "";

            return (
              <div
                key={product.id}
                className="text-center relative group cursor-pointer"
                data-aos="fade-up"
                data-aos-delay={100 + index * 100}
              >
                <div className="bg-white p-8 mb-6 flex items-center justify-center h-64 relative overflow-hidden">
                  <div className="w-full h-full bg-gray-50 rounded-lg flex items-center justify-center relative">
                    {image && !erroredImages[product.id] ? (
                      <Image
                        src={image}
                        alt={product.name}
                        fill
                        sizes="(max-width: 768px) 100vw, 300px"
                        style={{ objectFit: "contain" }}
                        onError={() => handleImageError(product.id)}
                      />
                    ) : (
                      <span className="text-6xl absolute inset-0 flex items-center justify-center">
                        🛒
                      </span>
                    )}
                  </div>

                  <div className="absolute inset-0 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                    <Link href={`/products/${product.id}`}>
                      <button className="bg-earthy-cocoa text-white px-4 py-2 rounded-lg font-medium shadow-lg transform translate-y-4 opacity-0 rotate-12 scale-75 group-hover:translate-y-0 group-hover:opacity-100 group-hover:rotate-0 group-hover:scale-100 transition-all duration-500 hover:scale-105">
                        View Product
                      </button>
                    </Link>
                  </div>
                </div>

                <div className="flex justify-center gap-1 mb-3">{renderStars()}</div>

                <h3 className="text-xl font-medium text-deep-charcoal mb-2">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-600 italic">
                  {product.specifications?.origin}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FeaturedProducts;
