import React from 'react'
import Link from "next/link";

export default function CareersSection(data) {
    const icons = [
        // Icon 1
        <svg key="icon1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 2L2 7v10c0 5.55 3.84 10 9 11 1.16.21 2.76.21 3.91 0C20.16 27 24 22.55 24 17V7l-10-5z"
          />
        </svg>,
        // Icon 2
        <svg key="icon2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>,
        // Icon 3
        <svg key="icon3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064"
          />
        </svg>,
        // Icon 4
        <svg key="icon4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>,
      ];
  return (
       <section
      className={`relative mx-4 sm:mx-6 lg:mx-8 xl:mx-20 my-6 sm:my-8 lg:my-12 xl:my-20 py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-12 xl:px-24 rounded-3xl overflow-hidden shadow-2xl flex flex-col lg:flex-row items-center gap-6 sm:gap-8 lg:gap-12 bg-gradient-to-br from-white via-gray-50 to-white border border-gray-200 ${className}`}
      data-aos="zoom-in"
      data-aos-duration="1000"
    >
      {/* Decorative blobs */}
      <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-harvest-green opacity-10 -translate-y-16 translate-x-16" />
      <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-forest-green opacity-10 translate-y-12 -translate-x-12" />

      {/* Left Content */}
      <div className="text-center lg:text-left flex-1 z-10" data-aos="fade-right" data-aos-delay="300">
        <h3 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-baru-bold text-deep-charcoal mb-4 sm:mb-6 leading-tight">
          {data.title}
        </h3>
        <p className="text-base sm:text-lg font-baru-regular text-earthy-cocoa leading-relaxed max-w-none lg:max-w-md mb-6 sm:mb-8" data-aos="fade-up" data-aos-delay="500">
          {data.description}
        </p>
        {data.ctaButton && (
          <Link href={data.ctaButton.href}>
            <button
              className="px-6 sm:px-8 lg:px-10 py-3 sm:py-4 rounded-xl border-2 border-forest-green bg-harvest-green text-deep-charcoal font-baru-semibold text-sm sm:text-base shadow-lg transition-transform duration-300 transform hover:scale-105 hover:shadow-xl"
              data-aos="fade-up"
              data-aos-delay="700"
            >
              <span className="flex items-center gap-2">
                {data.ctaButton.text}
                <svg className="w-4 h-4 sm:w-5 sm:h-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </span>
            </button>
          </Link>
        )}
      </div>

      {/* Right Icon Grid */}
      <div
        className="w-full lg:w-[50%] h-64 sm:h-80 lg:h-96 rounded-2xl flex items-center justify-center relative overflow-hidden shadow-xl bg-gradient-to-br from-forest-green to-harvest-green border-2 border-sun-gold"
        data-aos="fade-left"
        data-aos-delay="400"
      >
        {/* Pattern overlay */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #FFFFFF 2px, transparent 2px),
                              radial-gradient(circle at 75% 75%, #FFFFFF 2px, transparent 2px)`,
            backgroundSize: "40px 40px",
          }}
        />

        <div className="relative z-20 text-center transform transition-all duration-500 group-hover:scale-105">
          <div className="grid grid-cols-2 gap-4 lg:gap-6 mb-4 sm:mb-6">
            {icons.map((icon, index) => (
              <div
                key={index}
                className={`w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 flex items-center justify-center rounded-2xl shadow-lg transition-all duration-300 hover:scale-110 hover:rotate-3`}
                style={{
                  backgroundColor: index % 2 === 0 ? "#FFCA00" : "#212121",
                  border: `2px solid ${index % 2 === 0 ? "#212121" : "#FFCA00"}`,
                }}
                data-aos="flip-left"
                data-aos-delay={600 + index * 100}
              >
                {React.cloneElement(icon, {
                  className: `w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 ${
                    index % 2 === 0 ? "text-deep-charcoal" : "text-white"
                  }`,
                })}
              </div>
            ))}
          </div>
          <p className="font-baru-semibold text-white text-base sm:text-lg tracking-wide" data-aos="fade-up" data-aos-delay="1000">
            {data.iconDescription}
          </p>
        </div>

        {/* Floating dots */}
        <div className="absolute top-4 right-4 w-3 h-3 rounded-full bg-sun-gold animate-pulse" />
        <div className="absolute bottom-6 left-6 w-2 h-2 rounded-full bg-white animate-pulse" />
      </div>

      {/* Section overlay for depth */}
      <div className="absolute inset-0 pointer-events-none rounded-3xl bg-gradient-to-br from-white/10 via-transparent to-deep-charcoal/5" />
    </section>
  )
}
