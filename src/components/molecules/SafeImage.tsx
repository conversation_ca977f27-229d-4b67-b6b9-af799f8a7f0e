"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { ImageIcon } from 'lucide-react';

interface SafeImageProps {
  src: string;
  alt: string;
  fill?: boolean;
  className?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
}

export default function SafeImage({ 
  src, 
  alt, 
  fill = false, 
  className = '', 
  width, 
  height,
  onLoad,
  onError 
}: SafeImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleImageError = () => {
    console.error('Failed to load image:', src);
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  if (imageError) {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-400">
          <ImageIcon size={32} className="mx-auto mb-2" />
          <p className="text-xs">Image failed to load</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {isLoading && (
        <div className={`bg-gray-100 animate-pulse flex items-center justify-center ${className}`}>
          <ImageIcon size={32} className="text-gray-400" />
        </div>
      )}
      <Image
        src={src}
        alt={alt}
        fill={fill}
        width={!fill ? width : undefined}
        height={!fill ? height : undefined}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        unoptimized={src.includes('helevon-technology-appwrite')} // Disable optimization for Appwrite images
      />
    </>
  );
}
