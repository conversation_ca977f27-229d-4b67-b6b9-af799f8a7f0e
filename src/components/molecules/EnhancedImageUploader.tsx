"use client";

import React, { useState, useRef } from 'react';
import { Upload, X, Star, Crown, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';

export interface ImageData {
  url: string;
  isMain: boolean;
  isFeatured: boolean;
  order: number;
  alt?: string;
}

interface EnhancedImageUploaderProps {
  onImagesChange: (images: ImageData[]) => void;
  initialImages?: ImageData[];
  maxImages?: number;
}

export default function EnhancedImageUploader({ 
  onImagesChange, 
  initialImages = [], 
  maxImages = 10 
}: EnhancedImageUploaderProps) {
  const [images, setImages] = useState<ImageData[]>(initialImages);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (files: FileList) => {
    if (files.length === 0) return;
    
    setUploading(true);
    
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });
        
        if (!response.ok) {
          throw new Error('Upload failed');
        }
        
        const data = await response.json();
        return data.url;
      });
      
      const uploadedUrls = await Promise.all(uploadPromises);
      
      const newImages: ImageData[] = uploadedUrls.map((url, index) => ({
        url,
        isMain: images.length === 0 && index === 0, // First image is main if no images exist
        isFeatured: false,
        order: images.length + index,
        alt: ''
      }));
      
      const updatedImages = [...images, ...newImages];
      setImages(updatedImages);
      onImagesChange(updatedImages);
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload images. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      handleFileUpload(files);
    }
  };

  const removeImage = (index: number) => {
    const updatedImages = images.filter((_, i) => i !== index);
    // Reorder remaining images
    const reorderedImages = updatedImages.map((img, i) => ({
      ...img,
      order: i,
      isMain: i === 0 && updatedImages.length > 0 // First image becomes main
    }));
    setImages(reorderedImages);
    onImagesChange(reorderedImages);
  };

  const setAsMain = (index: number) => {
    const updatedImages = images.map((img, i) => ({
      ...img,
      isMain: i === index
    }));
    setImages(updatedImages);
    onImagesChange(updatedImages);
  };

  const setAsFeatured = (index: number) => {
    const updatedImages = images.map((img, i) => ({
      ...img,
      isFeatured: i === index
    }));
    setImages(updatedImages);
    onImagesChange(updatedImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    
    // Update order
    const reorderedImages = updatedImages.map((img, i) => ({
      ...img,
      order: i
    }));
    
    setImages(reorderedImages);
    onImagesChange(reorderedImages);
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        className="border-2 border-dashed border-earth-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors"
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="space-y-2">
          <Upload className="mx-auto h-12 w-12 text-earth-400" />
          <div>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="text-primary-600 hover:text-primary-500 font-medium"
              disabled={uploading}
            >
              {uploading ? 'Uploading...' : 'Click to upload'}
            </button>
            <p className="text-earth-500"> or drag and drop</p>
          </div>
          <p className="text-sm text-earth-400">
            PNG, JPG, GIF up to 10MB (max {maxImages} images)
          </p>
        </div>
      </div>

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative group border rounded-lg overflow-hidden bg-white shadow-sm"
            >
              <div className="aspect-square relative">
                <Image
                  src={image.url}
                  alt={image.alt || `Product image ${index + 1}`}
                  fill
                  className="object-cover"
                />
                
                {/* Overlay with controls */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                    <button
                      onClick={() => setAsMain(index)}
                      className={`p-2 rounded-full ${
                        image.isMain 
                          ? 'bg-yellow-500 text-white' 
                          : 'bg-white text-gray-700 hover:bg-yellow-100'
                      }`}
                      title="Set as main image"
                    >
                      <Crown size={16} />
                    </button>
                    
                    <button
                      onClick={() => setAsFeatured(index)}
                      className={`p-2 rounded-full ${
                        image.isFeatured 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-white text-gray-700 hover:bg-blue-100'
                      }`}
                      title="Set as featured image"
                    >
                      <Star size={16} />
                    </button>
                    
                    <button
                      onClick={() => removeImage(index)}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
                      title="Remove image"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Image labels */}
              <div className="p-2 space-y-1">
                <div className="flex flex-wrap gap-1">
                  {image.isMain && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <Crown size={12} className="mr-1" />
                      Main
                    </span>
                  )}
                  {image.isFeatured && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <Star size={12} className="mr-1" />
                      Featured
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500">Order: {index + 1}</p>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {images.length === 0 && (
        <div className="text-center py-8 text-earth-500">
          <ImageIcon className="mx-auto h-12 w-12 mb-2" />
          <p>No images uploaded yet</p>
        </div>
      )}
    </div>
  );
}
