"use client";

import React, { useState, useEffect } from 'react';
import { X, Package, CheckCircle, Send, Loader2 } from 'lucide-react';
import { Button } from '@/components/atoms/Button';

interface Product {
  $id: string;
  name: string;
  price: string;
  description: string;
}

interface QuoteRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  preSelectedProductId?: string;
}

interface FormData {
  name: string;
  email: string;
  company: string;
  phone: string;
  message: string;
  selectedProducts: string[];
  otherProducts: string;
}

export default function QuoteRequestModal({ 
  isOpen, 
  onClose, 
  preSelectedProductId 
}: QuoteRequestModalProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    selectedProducts: preSelectedProductId ? [preSelectedProductId] : [],
    otherProducts: ''
  });

  // Fetch products when modal opens
  useEffect(() => {
    if (isOpen && products.length === 0) {
      fetchProducts();
    }
  }, [isOpen]);

  // Update selected products when preSelectedProductId changes
  useEffect(() => {
    if (preSelectedProductId && !formData.selectedProducts.includes(preSelectedProductId)) {
      setFormData(prev => ({
        ...prev,
        selectedProducts: [...prev.selectedProducts, preSelectedProductId]
      }));
    }
  }, [preSelectedProductId]);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleProductToggle = (productId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedProducts: prev.selectedProducts.includes(productId)
        ? prev.selectedProducts.filter(id => id !== productId)
        : [...prev.selectedProducts, productId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      // Get selected product names
      const selectedProductNames = products
        .filter(p => formData.selectedProducts.includes(p.$id))
        .map(p => p.name);

      // Format message with selected products
      const productList = selectedProductNames.length > 0 
        ? `\n\nRequested Products:\n${selectedProductNames.map(name => `• ${name}`).join('\n')}`
        : '';
      
      const otherProductsText = formData.otherProducts.trim() 
        ? `\n\nOther Products/Services:\n${formData.otherProducts}`
        : '';

      const fullMessage = `${formData.message}${productList}${otherProductsText}`;

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          company: formData.company,
          phone: formData.phone,
          message: fullMessage,
          type: 'quote_request'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send quote request');
      }

      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
        setFormData({
          name: '',
          email: '',
          company: '',
          phone: '',
          message: '',
          selectedProducts: preSelectedProductId ? [preSelectedProductId] : [],
          otherProducts: ''
        });
      }, 2000);

    } catch (error: any) {
      setError(error.message || 'Failed to send quote request. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-20 backdrop-blur-md transition-all duration-300"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="absolute right-0 top-0 h-full w-full max-w-2xl bg-white shadow-2xl transform transition-transform animate-slide-in-right">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-emerald-600 to-emerald-700">
            <div>
              <h2 className="text-2xl font-bold text-white">Request Quote</h2>
              <p className="text-emerald-100 mt-1">Get pricing for our premium products</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-colors"
            >
              <X size={24} className="text-white" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {success ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <CheckCircle size={64} className="text-emerald-600 mb-4" />
                <h3 className="text-2xl font-bold text-emerald-800 mb-2">Quote Request Sent!</h3>
                <p className="text-gray-600">We'll get back to you within 24 hours with a detailed quote.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Contact Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company
                      </label>
                      <input
                        type="text"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="Your company name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="Your phone number"
                      />
                    </div>
                  </div>
                </div>

                {/* Product Selection */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Products</h3>
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 size={32} className="animate-spin text-emerald-600" />
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-4">
                      {products.map((product) => (
                        <div key={product.$id} className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id={`product-${product.$id}`}
                            checked={formData.selectedProducts.includes(product.$id)}
                            onChange={() => handleProductToggle(product.$id)}
                            className="mt-1 h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`product-${product.$id}`} className="flex-1 cursor-pointer">
                            <div className="flex items-start space-x-3">
                              <Package size={16} className="text-emerald-600 mt-1 flex-shrink-0" />
                              <div>
                                <h4 className="font-medium text-gray-900">{product.name}</h4>
                                <p className="text-sm text-gray-600">{product.price}</p>
                                <p className="text-xs text-gray-500 mt-1 line-clamp-2">{product.description}</p>
                              </div>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Other Products */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Other Products/Services
                  </label>
                  <textarea
                    name="otherProducts"
                    value={formData.otherProducts}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="Describe any other products or services you're interested in..."
                  />
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="Please provide details about your requirements, quantities, delivery location, etc."
                  />
                </div>

                {/* Error Message */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-red-700 text-sm">{error}</p>
                  </div>
                )}

                {/* Submit Button */}
                <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={submitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={submitting}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    {submitting ? (
                      <>
                        <Loader2 size={16} className="animate-spin mr-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send size={16} className="mr-2" />
                        Send Quote Request
                      </>
                    )}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
