"use client";

import { useEffect, useState } from 'react';

interface SafeDOMWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
}

/**
 * SafeDOMWrapper - A component that ensures DOM operations are safe
 * by waiting for the DOM to be fully ready before rendering children
 */
export default function SafeDOMWrapper({ 
  children, 
  fallback = null, 
  delay = 100 
}: SafeDOMWrapperProps) {
  const [isDOMReady, setIsDOMReady] = useState(false);

  useEffect(() => {
    // Ensure we're in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    // Check if DOM is already ready
    if (document.readyState === 'complete') {
      setIsDOMReady(true);
      return;
    }

    // Wait for DOM to be ready with a small delay for safety
    const timer = setTimeout(() => {
      try {
        // Additional checks to ensure DOM stability
        if (document.head && document.body && document.readyState !== 'loading') {
          setIsDOMReady(true);
        }
      } catch (error) {
        // If there's any error, still set as ready to prevent infinite loading
        setIsDOMReady(true);
      }
    }, delay);

    // Also listen for DOM ready events
    const handleDOMReady = () => {
      clearTimeout(timer);
      setIsDOMReady(true);
    };

    // Listen for various ready states
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', handleDOMReady);
      window.addEventListener('load', handleDOMReady);
    } else {
      // DOM is already ready
      handleDOMReady();
    }

    return () => {
      clearTimeout(timer);
      document.removeEventListener('DOMContentLoaded', handleDOMReady);
      window.removeEventListener('load', handleDOMReady);
    };
  }, [delay]);

  // Render fallback while DOM is not ready
  if (!isDOMReady) {
    return <>{fallback}</>;
  }

  // Render children when DOM is ready
  return <>{children}</>;
}

/**
 * Hook for safe DOM operations
 */
export const useSafeDOM = () => {
  const [isDOMReady, setIsDOMReady] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    const checkDOMReady = () => {
      try {
        if (document.head && document.body && document.readyState !== 'loading') {
          setIsDOMReady(true);
          return true;
        }
      } catch (error) {
        // Silently handle errors
      }
      return false;
    };

    if (checkDOMReady()) {
      return;
    }

    const timer = setTimeout(() => {
      checkDOMReady();
    }, 50);

    const handleReady = () => {
      clearTimeout(timer);
      setIsDOMReady(true);
    };

    document.addEventListener('DOMContentLoaded', handleReady);
    window.addEventListener('load', handleReady);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('DOMContentLoaded', handleReady);
      window.removeEventListener('load', handleReady);
    };
  }, []);

  const safeExecute = (operation: () => void) => {
    if (!isDOMReady || typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      operation();
    } catch (error) {
      // Silently handle DOM operation errors
    }
  };

  return {
    isDOMReady,
    safeExecute
  };
};
