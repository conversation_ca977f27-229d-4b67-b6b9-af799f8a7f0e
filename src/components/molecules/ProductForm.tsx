"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/atoms/Button";
import EnhancedImageUploader, { ImageData } from "@/components/molecules/EnhancedImageUploader";



export function ProductForm({
  initialData,
  onSubmit,
  isEditMode = false,
}) {
  const [formData, setFormData] = useState({
    name: "",
    categories: [] as string[],
    tags: [] as string[],
    description: "",
    details: "",
    price: "",
    stock: "Available",
    images: [] as ImageData[],
    specifications: {
      origin: "",
      quality: "",
      packaging: "",
      certifications: [""],
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);


  // Initialize form with initial data if provided
  useEffect(() => {
    if (initialData) {
      // Extract category and tag IDs from the initial data
      const categoryIds = initialData?.categories
        ? initialData?.categories?.map((cat) => cat.$id)
        : [];

      const tagIds = initialData.tags
        ? initialData?.tags?.map((tag) => tag.$id)
        : [];

      // Handle specifications - they come as an object from the API
      const specifications = initialData?.specifications
        ? {
            origin: initialData.specifications.origin || "",
            quality: initialData.specifications.quality || "",
            packaging: initialData.specifications.packaging || "",
            certifications: Array.isArray(initialData.specifications.certifications)
              ? initialData.specifications.certifications
              : typeof initialData.specifications.certifications === 'string'
              ? JSON.parse(initialData.specifications.certifications).filter(Boolean)
              : [""]
          }
        : {
            origin: "",
            quality: "",
            packaging: "",
            certifications: [""],
          };

      // Convert existing images to new format
      const imageData: ImageData[] = initialData.images && initialData.images.length > 0
        ? initialData.images.map((img: any, index: number) => ({
            url: img.url,
            isMain: img.isMain || index === 0,
            isFeatured: img.isFeatured || false,
            order: img.order || index,
            alt: img.alt || ''
          }))
        : [];

      setFormData({
        ...initialData,
        categories: categoryIds,
        tags: tagIds,
        images: imageData,
        specifications: specifications || {
          origin: "",
          quality: "",
          packaging: "",
          certifications: [""],
        },
      });

      // Set preview image from the main image or first image
      const mainImage = imageData.find(img => img.isMain) || imageData[0];
      if (mainImage) {
        setPreviewImage(mainImage.url);
      } else {
        setPreviewImage(null);
      }
    }
  }, [initialData]);

  // State for categories and tags from API
  const [categoryOptions, setCategoryOptions] = useState<
    { $id: string; name: string }[]
  >([]);
  const [tagOptions, setTagOptions] = useState<{ $id: string; name: string }[]>(
    []
  );
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);

  // Fetch categories and tags
  useEffect(() => {
    const fetchOptions = async () => {
      setIsLoadingOptions(true);

      try {
        // Fetch categories
        const categoriesResponse = await fetch("/api/categories");
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategoryOptions(categoriesData);
        }

        // Fetch tags
        const tagsResponse = await fetch("/api/tags");
        if (tagsResponse.ok) {
          const tagsData = await tagsResponse.json();
          setTagOptions(tagsData);
        }
      } catch (error) {
        console.error("Error fetching options:", error);
      } finally {
        setIsLoadingOptions(false);
      }
    };

    fetchOptions();
  }, []);

  // Stock options
  const stockOptions = ["Available", "Limited", "Out of Stock"];

  // Certification options
  const certificationOptions = [
    "Organic",
    "Fair Trade",
    "Non-GMO",
    "HACCP",
    "ISO 22000",
    "FDA Approved",
    "Rainforest Alliance",
    "UTZ Certified",
    "USDA Approved",
    "RSPO",
  ];

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...((prev[parent as keyof typeof prev] as object) || {}),
          [child]: value,
        },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear error when user types
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle certification changes
  const handleCertificationChange = (index: number, value: string) => {
    const updatedCertifications = [...formData.specifications.certifications];
    updatedCertifications[index] = value;

    setFormData((prev) => ({
      ...prev,
      specifications: {
        ...prev.specifications,
        certifications: updatedCertifications,
      },
    }));
  };

  // Add new certification field
  const addCertification = () => {
    setFormData((prev) => ({
      ...prev,
      specifications: {
        ...prev.specifications,
        certifications: [...prev.specifications.certifications, ""],
      },
    }));
  };

  // Remove certification field
  const removeCertification = (index: number) => {
    const updatedCertifications = [...formData.specifications.certifications];
    updatedCertifications.splice(index, 1);

    setFormData((prev) => ({
      ...prev,
      specifications: {
        ...prev.specifications,
        certifications: updatedCertifications,
      },
    }));
  };

  // Handle images change
  const handleImagesChange = (images: ImageData[]) => {
    setFormData((prev) => ({ ...prev, images }));
    // Update preview image to main image or first image
    const mainImage = images.find(img => img.isMain) || images[0];
    setPreviewImage(mainImage ? mainImage.url : null);
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Product name is required";
    }

    if (!formData.categories || formData.categories.length === 0) {
      newErrors.categories = "At least one category is required";
    }

    if (!formData.images || formData.images.length === 0) {
      newErrors.images = "At least one product image is required";
    } else if (!formData.images.some(img => img.isMain)) {
      newErrors.images = "Please set one image as the main image";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (!formData.price.trim()) {
      newErrors.price = "Price is required";
    }

    if (!formData.specifications.origin.trim()) {
      newErrors["specifications.origin"] = "Origin is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Filter out empty certifications
      const filteredCertifications =
        formData.specifications.certifications.filter(
          (cert) => cert.trim() !== ""
        );

      const submissionData = {
        ...formData,
        specifications: {
          ...formData.specifications,
          certifications: filteredCertifications,
        },
      };

      // Call the onSubmit callback with the form data
      onSubmit(submissionData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left Column - Basic Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-earth-900 border-b border-earth-200 pb-2">
            Basic Information
          </h3>

          {/* Product Name */}
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Product Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ""}
              onChange={handleChange}
              className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.name ? "border-red-500" : "border-earth-200"
              }`}
              placeholder="e.g., Organic Cashew Nuts"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          {/* Categories */}
          <div>
            <label
              htmlFor="categories"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Categories <span className="text-red-500">*</span>
            </label>
            <div className="space-y-2">
              {isLoadingOptions ? (
                <div className="flex items-center space-x-2 text-sm text-earth-500">
                  <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-primary-500 rounded-full"></div>
                  <span>Loading categories...</span>
                </div>
              ) : (
                categoryOptions?.map((category) => (
                  <div key={category.$id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`category-${category.$id}`}
                      name={`category-${category.$id}`}
                      checked={
                        formData.categories?.includes(category.$id) || false
                      }
                      onChange={(e) => {
                        const isChecked = e.target.checked;
                        setFormData((prev) => {
                          const currentCategories = prev.categories || [];
                          if (isChecked) {
                            return {
                              ...prev,
                              categories: [...currentCategories, category.$id],
                            };
                          } else {
                            return {
                              ...prev,
                              categories: currentCategories.filter(
                                (id) => id !== category.$id
                              ),
                            };
                          }
                        });
                      }}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-earth-300 rounded"
                    />
                    <label
                      htmlFor={`category-${category.$id}`}
                      className="ml-2 text-sm text-earth-700"
                    >
                      {category.name}
                    </label>
                  </div>
                ))
              )}
            </div>
            {errors.categories && (
              <p className="mt-1 text-sm text-red-500">{errors.categories}</p>
            )}
          </div>

          {/* Tags */}
          <div>
            <label
              htmlFor="tags"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Tags
            </label>
            <div className="space-y-2">
              {isLoadingOptions ? (
                <div className="flex items-center space-x-2 text-sm text-earth-500">
                  <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-primary-500 rounded-full"></div>
                  <span>Loading tags...</span>
                </div>
              ) : (
                tagOptions?.map((tag) => (
                  <div key={tag.$id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`tag-${tag.$id}`}
                      name={`tag-${tag.$id}`}
                      checked={formData.tags?.includes(tag.$id) || false}
                      onChange={(e) => {
                        const isChecked = e.target.checked;
                        setFormData((prev) => {
                          const currentTags = prev.tags || [];
                          if (isChecked) {
                            return { ...prev, tags: [...currentTags, tag.$id] };
                          } else {
                            return {
                              ...prev,
                              tags: currentTags.filter((id) => id !== tag.$id),
                            };
                          }
                        });
                      }}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-earth-300 rounded"
                    />
                    <label
                      htmlFor={`tag-${tag.$id}`}
                      className="ml-2 text-sm text-earth-700"
                    >
                      {tag.name}
                    </label>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Description */}
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Short Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={2}
              className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.description ? "border-red-500" : "border-earth-200"
              }`}
              placeholder="Brief description of the product"
            ></textarea>
            {errors.description && (
              <p className="mt-1 text-sm text-red-500">{errors.description}</p>
            )}
          </div>

          {/* Detailed Description */}
          <div>
            <label
              htmlFor="details"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Detailed Description
            </label>
            <textarea
              id="details"
              name="details"
              value={formData.details}
              onChange={handleChange}
              rows={4}
              className="w-full px-4 py-2 border border-earth-200 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Comprehensive details about the product"
            ></textarea>
          </div>

          {/* Price */}
          <div>
            <label
              htmlFor="price"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Price Range <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="price"
              name="price"
              value={formData.price || ""}
              onChange={handleChange}
              className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.price ? "border-red-500" : "border-earth-200"
              }`}
              placeholder="e.g., $1,200 - $1,500 per ton"
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-500">{errors.price}</p>
            )}
          </div>

          {/* Stock Status */}
          <div>
            <label
              htmlFor="stock"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Stock Status
            </label>
            <select
              id="stock"
              name="stock"
              value={formData.stock}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-earth-200 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {stockOptions?.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Right Column - Specifications and Image */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-earth-900 border-b border-earth-200 pb-2">
            Specifications & Image
          </h3>

          {/* Origin */}
          <div>
            <label
              htmlFor="specifications.origin"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Origin <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="specifications.origin"
              name="specifications.origin"
              value={formData.specifications.origin || ""}
              onChange={handleChange}
              className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors["specifications.origin"]
                  ? "border-red-500"
                  : "border-earth-200"
              }`}
              placeholder="e.g., West Africa, Ghana"
            />
            {errors["specifications.origin"] && (
              <p className="mt-1 text-sm text-red-500">
                {errors["specifications.origin"]}
              </p>
            )}
          </div>

          {/* Quality */}
          <div>
            <label
              htmlFor="specifications.quality"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Quality
            </label>
            <input
              type="text"
              id="specifications.quality"
              name="specifications.quality"
              value={formData.specifications.quality || ""}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-earth-200 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., Premium grade, 99.5% purity"
            />
          </div>

          {/* Packaging */}
          <div>
            <label
              htmlFor="specifications.packaging"
              className="block text-sm font-medium text-earth-700 mb-1"
            >
              Packaging
            </label>
            <input
              type="text"
              id="specifications.packaging"
              name="specifications.packaging"
              value={formData.specifications.packaging || ""}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-earth-200 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., 25kg bags, bulk containers"
            />
          </div>

        {/* Certifications */}
            <div>
              <label className="block text-sm font-medium text-earth-700 mb-1">
                Certifications
              </label>
              {formData.specifications.certifications.map((cert, index) => (
                <div key={index} className="flex items-center mb-2">
                  <select
                    value={cert}
                    onChange={(e) =>
                      handleCertificationChange(index, e.target.value)
                    }
                    className="w-full px-4 py-2 border border-earth-200 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">Select Certification</option>
                    {certificationOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                  {formData.specifications.certifications.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeCertification(index)}
                      className="ml-2 text-red-600 hover:underline"
                    >
                      Remove
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addCertification}
                className="text-sm text-primary-600 hover:underline"
              >
                + Add Certification
              </button>
            </div>

          {/* Product Images */}
          <div>
            <label className="block text-sm font-medium text-earth-700 mb-2">
              Product Images <span className="text-red-500">*</span>
            </label>
            <p className="text-sm text-earth-600 mb-3">
              Upload multiple images. Set one as main (required) and optionally one as featured.
            </p>
            <EnhancedImageUploader
              initialImages={formData.images}
              onImagesChange={handleImagesChange}
              maxImages={10}
            />
            {errors.images && (
              <p className="mt-2 text-sm text-red-500">{errors.images}</p>
            )}
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button variant="outline" type="button" href="/dashboard/products">
          Cancel
        </Button>
        <Button type="submit" isLoading={isSubmitting} className="bg-emerald-600 text-white hover:bg-primary-700">
            {isSubmitting
            ? isEditMode
              ? "Updating..."
              : "Submitting..."
            : isEditMode
            ? "Update Product"
            : "Create Product"}
        </Button>
      </div>
    </form>
  );
}
export default ProductForm;
