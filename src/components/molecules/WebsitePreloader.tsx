"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface PreloaderProps {
  onLoadComplete?: () => void;
}

const colorVariations = [
  {
    name: 'Deep Charcoal',
    pattern: '/assets/Afric Source Patterns/PNG Files /Deep Charcoal_.png',
    logo: '/assets/Afric Source Logo Plus Text/Deep Charcoal_.png',
    bgColor: 'bg-gray-900',
    textColor: 'text-gray-100'
  },
  {
    name: 'Earthy Cocoa',
    pattern: '/assets/Afric Source Patterns/PNG Files /Earthy Cocoa_.png',
    logo: '/assets/Afric Source Logo Plus Text/Earthy Cocoa.png',
    bgColor: 'bg-amber-800',
    textColor: 'text-amber-100'
  },
  {
    name: 'Forest Green',
    pattern: '/assets/Afric Source Patterns/PNG Files /Forest Green_.png',
    logo: '/assets/Afric Source Logo Plus Text/Forest Green.png',
    bgColor: 'bg-emerald-800',
    textColor: 'text-emerald-100'
  },
  {
    name: 'Harvest Green',
    pattern: '/assets/Afric Source Patterns/PNG Files /Harvest Green_.png',
    logo: '/assets/Afric Source Logo Plus Text/Harvest Green_.png',
    bgColor: 'bg-green-700',
    textColor: 'text-green-100'
  },
  {
    name: 'Sun Gold',
    pattern: '/assets/Afric Source Patterns/PNG Files /Sun Gold_.png',
    logo: '/assets/Afric Source Logo Plus Text/Sun Gold.png',
    bgColor: 'bg-yellow-500',
    textColor: 'text-yellow-900'
  },
  {
    name: 'White',
    pattern: '/assets/Afric Source Patterns/PNG Files /White.png',
    logo: '/assets/Afric Source Logo Plus Text/White.png',
    bgColor: 'bg-white',
    textColor: 'text-gray-800'
  }
];

export default function WebsitePreloader({ onLoadComplete }: PreloaderProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [currentColorIndex, setCurrentColorIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Color cycling effect
  useEffect(() => {
    const colorInterval = setInterval(() => {
      setCurrentColorIndex((prev) => (prev + 1) % colorVariations.length);
    }, 800); // Change color every 800ms for smooth transitions

    return () => clearInterval(colorInterval);
  }, []);

  // Simulate loading progress
  useEffect(() => {
    const progressInterval = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 15; // Random increment for realistic loading
      });
    }, 150);

    return () => clearInterval(progressInterval);
  }, []);

  // Handle loading completion
  useEffect(() => {
    if (loadingProgress >= 100) {
      const timer = setTimeout(() => {
        setIsLoading(false);
        setTimeout(() => {
          setIsVisible(false);
          onLoadComplete?.();
        }, 500); // Fade out duration
      }, 800); // Show complete state briefly

      return () => clearTimeout(timer);
    }
  }, [loadingProgress, onLoadComplete]);

  // Hide preloader when page is fully loaded
  useEffect(() => {
    const handleLoad = () => {
      setLoadingProgress(100);
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
      return () => window.removeEventListener('load', handleLoad);
    }
  }, []);

  if (!isVisible) return null;

  const currentColor = colorVariations[currentColorIndex];

  return (
    <div 
      className={`fixed inset-0 z-[9999] flex items-center justify-center transition-all duration-500 ${
        isLoading ? 'opacity-100' : 'opacity-0'
      } ${currentColor.bgColor}`}
      style={{
        background: `linear-gradient(135deg, ${
          currentColorIndex === 0 ? '#1f2937, #374151' :
          currentColorIndex === 1 ? '#92400e, #d97706' :
          currentColorIndex === 2 ? '#065f46, #10b981' :
          currentColorIndex === 3 ? '#15803d, #22c55e' :
          currentColorIndex === 4 ? '#eab308, #fbbf24' :
          '#ffffff, #f3f4f6'
        })`,
        transition: 'background 800ms ease-in-out'
      }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <Image
          src={currentColor.pattern}
          alt="AfricSource Pattern"
          fill
          className="object-cover transition-opacity duration-800"
          priority
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center text-center px-4">
        {/* Logo */}
        <div className="mb-8 transition-all duration-800 transform">
          <Image
            src={currentColor.logo}
            alt="AfricSource Logo"
            width={300}
            height={120}
            className="w-64 h-auto md:w-80 lg:w-96 transition-all duration-800"
            priority
          />
        </div>

        {/* Loading Text */}
        <div className={`mb-6 transition-colors duration-800 ${currentColor.textColor}`}>
          <h2 className="text-xl md:text-2xl font-semibold mb-2">
            {isLoading ? 'Loading Premium African Products...' : 'Welcome to AfricSource'}
          </h2>
          <p className="text-sm md:text-base opacity-80">
            {isLoading ? 'Preparing your experience' : 'Ready to explore'}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="w-64 md:w-80 bg-black bg-opacity-20 rounded-full h-2 mb-4">
          <div 
            className="h-2 rounded-full transition-all duration-300 ease-out"
            style={{
              width: `${Math.min(loadingProgress, 100)}%`,
              background: currentColorIndex === 5 ? '#374151' : 'rgba(255, 255, 255, 0.8)'
            }}
          />
        </div>

        {/* Progress Percentage */}
        <div className={`text-sm font-medium transition-colors duration-800 ${currentColor.textColor}`}>
          {Math.round(Math.min(loadingProgress, 100))}%
        </div>

        {/* Loading Dots Animation */}
        <div className="flex space-x-1 mt-4">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={`w-2 h-2 rounded-full transition-colors duration-800 animate-pulse`}
              style={{
                backgroundColor: currentColorIndex === 5 ? '#374151' : 'rgba(255, 255, 255, 0.6)',
                animationDelay: `${i * 200}ms`
              }}
            />
          ))}
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 opacity-20">
        <div 
          className="w-full h-full rounded-full animate-float"
          style={{
            background: currentColorIndex === 5 ? '#374151' : 'rgba(255, 255, 255, 0.3)'
          }}
        />
      </div>
      <div className="absolute bottom-10 right-10 w-16 h-16 opacity-20">
        <div 
          className="w-full h-full rounded-full animate-float"
          style={{
            background: currentColorIndex === 5 ? '#374151' : 'rgba(255, 255, 255, 0.3)',
            animationDelay: '2s'
          }}
        />
      </div>
      <div className="absolute top-1/2 left-20 w-12 h-12 opacity-20">
        <div 
          className="w-full h-full rounded-full animate-float"
          style={{
            background: currentColorIndex === 5 ? '#374151' : 'rgba(255, 255, 255, 0.3)',
            animationDelay: '4s'
          }}
        />
      </div>
    </div>
  );
}
