"use client";

import { useEffect, useState } from 'react';

interface FaviconState {
  isTabActive: boolean;
  isDarkMode: boolean;
  originalTitle: string;
}

export default function DynamicFavicon() {
  const [faviconState, setFaviconState] = useState<FaviconState>({
    isTabActive: true,
    isDarkMode: false,
    originalTitle: ''
  });

  // Favicon paths
  const faviconPaths = {
    white: '/assets/favicons/White_.png',
    earthyCocoa: '/assets/favicons/Earthy Cocoa_.png',
    harvestGreen: '/assets/favicons/Harvest Green_.png'
  };

  // Away message
  const awayTitle = "Request a quote! - AfricSource";

  // Function to update favicon with caching
  const updateFavicon = (iconPath: string) => {
    // Prevent unnecessary updates
    const currentFavicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
    if (currentFavicon && currentFavicon.href.includes(iconPath.split('/').pop() || '')) {
      return;
    }

    // Remove existing favicon links
    const existingLinks = document.querySelectorAll('link[rel*="icon"]');
    existingLinks.forEach(link => link.remove());

    // Create new favicon link with cache busting
    const link = document.createElement('link');
    link.rel = 'icon';
    link.type = 'image/png';
    link.href = `${iconPath}?v=${Date.now()}`;
    document.head.appendChild(link);

    // Also update apple-touch-icon for better mobile support
    const appleTouchLink = document.createElement('link');
    appleTouchLink.rel = 'apple-touch-icon';
    appleTouchLink.href = `${iconPath}?v=${Date.now()}`;
    document.head.appendChild(appleTouchLink);

    // Add shortcut icon for older browsers
    const shortcutLink = document.createElement('link');
    shortcutLink.rel = 'shortcut icon';
    shortcutLink.href = `${iconPath}?v=${Date.now()}`;
    document.head.appendChild(shortcutLink);
  };

  // Function to get appropriate favicon based on state
  const getAppropriateIcon = (isActive: boolean, isDark: boolean): string => {
    if (!isActive) {
      return faviconPaths.harvestGreen;
    }
    return isDark ? faviconPaths.white : faviconPaths.earthyCocoa;
  };

  // Function to update document title with smooth transition
  const updateTitle = (isActive: boolean, originalTitle: string) => {
    // Prevent unnecessary updates
    if (!isActive && document.title === awayTitle) return;
    if (isActive && document.title === originalTitle) return;

    if (!isActive && originalTitle) {
      // Add a small delay when switching away to prevent flicker
      setTimeout(() => {
        if (document.hidden) { // Double-check tab is still inactive
          document.title = awayTitle;
        }
      }, 100);
    } else if (isActive && originalTitle) {
      // Immediate restore when returning
      document.title = originalTitle;
    }
  };

  // Debounce function for performance
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // Detect system theme preference
  const detectTheme = (): boolean => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  };

  // Initialize favicon system
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      // Store original title
      const originalTitle = document.title;
      const isDarkMode = detectTheme();

    setFaviconState(prev => ({
      ...prev,
      originalTitle,
      isDarkMode
    }));

    // Set initial favicon
    updateFavicon(getAppropriateIcon(true, isDarkMode));

    // Listen for theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleThemeChange = (e: MediaQueryListEvent) => {
      const newIsDarkMode = e.matches;
      setFaviconState(prev => {
        const newState = { ...prev, isDarkMode: newIsDarkMode };
        updateFavicon(getAppropriateIcon(newState.isTabActive, newIsDarkMode));
        return newState;
      });
    };

    mediaQuery.addEventListener('change', handleThemeChange);

    // Page Visibility API for tab focus detection
    const handleVisibilityChange = () => {
      const isActive = !document.hidden;
      
      setFaviconState(prev => {
        const newState = { ...prev, isTabActive: isActive };
        
        // Update favicon immediately
        updateFavicon(getAppropriateIcon(isActive, prev.isDarkMode));
        
        // Update title immediately
        updateTitle(isActive, prev.originalTitle);
        
        return newState;
      });
    };

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Also listen for window focus/blur as fallback
    const handleFocus = () => {
      setFaviconState(prev => {
        const newState = { ...prev, isTabActive: true };
        updateFavicon(getAppropriateIcon(true, prev.isDarkMode));
        updateTitle(true, prev.originalTitle);
        return newState;
      });
    };

    const handleBlur = () => {
      setFaviconState(prev => {
        const newState = { ...prev, isTabActive: false };
        updateFavicon(getAppropriateIcon(false, prev.isDarkMode));
        updateTitle(false, prev.originalTitle);
        return newState;
      });
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

      // Cleanup
      return () => {
        mediaQuery.removeEventListener('change', handleThemeChange);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('focus', handleFocus);
        window.removeEventListener('blur', handleBlur);
      };
    } catch (error) {
      // Silently handle errors to prevent breaking the app
      console.warn('DynamicFavicon initialization failed:', error);
    }
  }, []);

  // Update favicon when state changes
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const iconPath = getAppropriateIcon(faviconState.isTabActive, faviconState.isDarkMode);
    updateFavicon(iconPath);
    updateTitle(faviconState.isTabActive, faviconState.originalTitle);
  }, [faviconState.isTabActive, faviconState.isDarkMode]);

  // This component doesn't render anything visible
  return null;
}

// Hook for manual favicon control (optional)
export const useDynamicFavicon = () => {
  const updateFaviconManually = (iconPath: string) => {
    if (typeof window === 'undefined') return;
    
    const existingLinks = document.querySelectorAll('link[rel*="icon"]');
    existingLinks.forEach(link => link.remove());

    const link = document.createElement('link');
    link.rel = 'icon';
    link.type = 'image/png';
    link.href = iconPath;
    document.head.appendChild(link);
  };

  const updateTitleManually = (title: string) => {
    if (typeof window === 'undefined') return;
    document.title = title;
  };

  return {
    updateFavicon: updateFaviconManually,
    updateTitle: updateTitleManually,
    faviconPaths: {
      white: '/assets/favicons/White_.png',
      earthyCocoa: '/assets/favicons/Earthy Cocoa_.png',
      harvestGreen: '/assets/favicons/Harvest Green_.png'
    }
  };
};
