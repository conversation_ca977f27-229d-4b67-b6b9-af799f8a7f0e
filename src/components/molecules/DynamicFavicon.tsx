"use client";

import { useEffect, useState } from 'react';
import SafeD<PERSON><PERSON>rapper from './SafeDOMWrapper';

interface FaviconState {
  isTabActive: boolean;
  isDarkMode: boolean;
  originalTitle: string;
}

function DynamicFaviconCore() {
  const [faviconState, setFaviconState] = useState<FaviconState>({
    isTabActive: true,
    isDarkMode: false,
    originalTitle: ''
  });

  // Favicon paths
  const faviconPaths = {
    white: '/assets/favicons/White_.png',
    earthyCocoa: '/assets/favicons/Earthy Cocoa_.png',
    harvestGreen: '/assets/favicons/Harvest Green_.png'
  };

  // Away message
  const awayTitle = "Request a quote! - AfricSource";

  // Function to update favicon with ultra-safe DOM manipulation
  const updateFavicon = (iconPath: string) => {
    // Early return if not in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      // Prevent unnecessary updates with more robust checking
      const currentFavicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      const iconFileName = iconPath.split('/').pop() || '';
      if (currentFavicon && currentFavicon.href && iconFileName && currentFavicon.href.includes(iconFileName)) {
        return;
      }

      // Use a more defensive approach to remove existing favicon links
      const existingLinks = Array.from(document.querySelectorAll('link[rel*="icon"]'));
      existingLinks.forEach(link => {
        try {
          // Multiple safety checks before removal
          if (link && link.parentNode && link.parentNode.contains && link.parentNode.contains(link)) {
            link.parentNode.removeChild(link);
          } else if (link && link.remove && typeof link.remove === 'function') {
            // Fallback to modern remove method if available
            link.remove();
          }
        } catch (removeError) {
          // Silently handle removal errors - this prevents the critical error
          try {
            // Last resort: try to set href to empty to disable the link
            if (link && 'href' in link) {
              (link as HTMLLinkElement).href = '';
            }
          } catch (fallbackError) {
            // Complete silence on fallback errors
          }
        }
      });

      // Wait a tick to ensure DOM is stable before adding new elements
      setTimeout(() => {
        try {
          // Verify document.head still exists
          if (!document.head) {
            return;
          }

          // Create new favicon link with cache busting
          const link = document.createElement('link');
          link.rel = 'icon';
          link.type = 'image/png';
          link.href = `${iconPath}?v=${Date.now()}`;

          // Safely append to head with additional checks
          if (document.head && document.head.appendChild) {
            document.head.appendChild(link);
          }

          // Also update apple-touch-icon for better mobile support
          const appleTouchLink = document.createElement('link');
          appleTouchLink.rel = 'apple-touch-icon';
          appleTouchLink.href = `${iconPath}?v=${Date.now()}`;

          if (document.head && document.head.appendChild) {
            document.head.appendChild(appleTouchLink);
          }

          // Add shortcut icon for older browsers
          const shortcutLink = document.createElement('link');
          shortcutLink.rel = 'shortcut icon';
          shortcutLink.href = `${iconPath}?v=${Date.now()}`;

          if (document.head && document.head.appendChild) {
            document.head.appendChild(shortcutLink);
          }
        } catch (appendError) {
          // Silently handle append errors
        }
      }, 0);

    } catch (error) {
      // Completely silent error handling to prevent any crashes
    }
  };

  // Function to get appropriate favicon based on state
  const getAppropriateIcon = (isActive: boolean, isDark: boolean): string => {
    if (!isActive) {
      return faviconPaths.harvestGreen;
    }
    return isDark ? faviconPaths.white : faviconPaths.earthyCocoa;
  };

  // Function to update document title with ultra-safe manipulation
  const updateTitle = (isActive: boolean, originalTitle: string) => {
    // Early return if not in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      // Prevent unnecessary updates with additional safety checks
      if (!isActive && document.title === awayTitle) return;
      if (isActive && document.title === originalTitle) return;

      if (!isActive && originalTitle) {
        // Add a small delay when switching away to prevent flicker
        setTimeout(() => {
          try {
            if (document && document.hidden && typeof document.title === 'string') {
              document.title = awayTitle;
            }
          } catch (titleError) {
            // Silently handle title update errors
          }
        }, 100);
      } else if (isActive && originalTitle) {
        // Immediate restore when returning with safety check
        try {
          if (document && typeof document.title === 'string') {
            document.title = originalTitle;
          }
        } catch (titleError) {
          // Silently handle title update errors
        }
      }
    } catch (error) {
      // Completely silent error handling
    }
  };

  // Safe event handler wrapper
  const safeEventHandler = (handler: () => void) => {
    return () => {
      try {
        handler();
      } catch (error) {
        console.warn('Event handler error:', error);
      }
    };
  };

  // Detect system theme preference
  const detectTheme = (): boolean => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  };

  // Initialize favicon system with defensive mounting
  useEffect(() => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    // Add a small delay to ensure DOM is fully ready
    const initTimeout = setTimeout(() => {
      try {
        // Verify document is ready and stable
        if (!document.head || !document.title) {
          return;
        }

        // Store original title
        const originalTitle = document.title;
        const isDarkMode = detectTheme();

    setFaviconState(prev => ({
      ...prev,
      originalTitle,
      isDarkMode
    }));

    // Set initial favicon
    updateFavicon(getAppropriateIcon(true, isDarkMode));

    // Listen for theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleThemeChange = (e: MediaQueryListEvent) => {
      const newIsDarkMode = e.matches;
      setFaviconState(prev => {
        const newState = { ...prev, isDarkMode: newIsDarkMode };
        updateFavicon(getAppropriateIcon(newState.isTabActive, newIsDarkMode));
        return newState;
      });
    };

    mediaQuery.addEventListener('change', handleThemeChange);

    // Page Visibility API for tab focus detection
    const handleVisibilityChange = safeEventHandler(() => {
      const isActive = !document.hidden;

      setFaviconState(prev => {
        const newState = { ...prev, isTabActive: isActive };

        // Update favicon immediately
        updateFavicon(getAppropriateIcon(isActive, prev.isDarkMode));

        // Update title immediately
        updateTitle(isActive, prev.originalTitle);

        return newState;
      });
    });

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Also listen for window focus/blur as fallback
    const handleFocus = safeEventHandler(() => {
      setFaviconState(prev => {
        const newState = { ...prev, isTabActive: true };
        updateFavicon(getAppropriateIcon(true, prev.isDarkMode));
        updateTitle(true, prev.originalTitle);
        return newState;
      });
    });

    const handleBlur = safeEventHandler(() => {
      setFaviconState(prev => {
        const newState = { ...prev, isTabActive: false };
        updateFavicon(getAppropriateIcon(false, prev.isDarkMode));
        updateTitle(false, prev.originalTitle);
        return newState;
      });
    });

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

        // Cleanup
        return () => {
          mediaQuery.removeEventListener('change', handleThemeChange);
          document.removeEventListener('visibilitychange', handleVisibilityChange);
          window.removeEventListener('focus', handleFocus);
          window.removeEventListener('blur', handleBlur);
        };
      } catch (error) {
        // Silently handle errors to prevent breaking the app
      }
    }, 50); // Small delay to ensure DOM stability

    // Cleanup timeout on unmount
    return () => {
      clearTimeout(initTimeout);
    };
  }, []);

  // Update favicon when state changes
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const iconPath = getAppropriateIcon(faviconState.isTabActive, faviconState.isDarkMode);
    updateFavicon(iconPath);
    updateTitle(faviconState.isTabActive, faviconState.originalTitle);
  }, [faviconState.isTabActive, faviconState.isDarkMode]);

  // This component doesn't render anything visible
  return null;
}

// Main component wrapped with SafeDOMWrapper
export default function DynamicFavicon() {
  return (
    <SafeDOMWrapper delay={100}>
      <DynamicFaviconCore />
    </SafeDOMWrapper>
  );
}

// Hook for manual favicon control (optional)
export const useDynamicFavicon = () => {
  const updateFaviconManually = (iconPath: string) => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    try {
      const existingLinks = Array.from(document.querySelectorAll('link[rel*="icon"]'));
      existingLinks.forEach(link => {
        try {
          if (link && link.parentNode && link.parentNode.contains && link.parentNode.contains(link)) {
            link.parentNode.removeChild(link);
          } else if (link && link.remove && typeof link.remove === 'function') {
            link.remove();
          }
        } catch (removeError) {
          // Silently handle removal errors
        }
      });

      if (document.head) {
        const link = document.createElement('link');
        link.rel = 'icon';
        link.type = 'image/png';
        link.href = iconPath;
        document.head.appendChild(link);
      }
    } catch (error) {
      // Silently handle favicon update errors
    }
  };

  const updateTitleManually = (title: string) => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    try {
      if (document && typeof document.title === 'string') {
        document.title = title;
      }
    } catch (error) {
      // Silently handle title update errors
    }
  };

  return {
    updateFavicon: updateFaviconManually,
    updateTitle: updateTitleManually,
    faviconPaths: {
      white: '/assets/favicons/White_.png',
      earthyCocoa: '/assets/favicons/Earthy Cocoa_.png',
      harvestGreen: '/assets/favicons/Harvest Green_.png'
    }
  };
};
