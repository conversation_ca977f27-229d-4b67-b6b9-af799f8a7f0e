# AfricSource Database Schema

This document outlines the required Appwrite database collections and their schemas for the AfricSource application.

## Database ID
```
687e7c56003ba09479b1
```

## Collections

### 1. users
**Collection ID:** `users`
**Purpose:** Store user authentication and role information

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| email | string | Yes | - | User email address |
| name | string | Yes | - | User full name |
| role | string | Yes | USER | User role (USER, ADMIN) |

**Indexes:**
- email (unique)
- role

**Permissions:**
- Read: users
- Write: users
- Create: any
- Update: users
- Delete: admins

### 2. products
**Collection ID:** `products`
**Purpose:** Store main product information

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| name | string | Yes | - | Product name |
| slug | string | Yes | - | URL-friendly product identifier |
| description | string | Yes | - | Short product description |
| details | string | No | - | Detailed product information |
| price | string | No | - | Product price |
| stock | string | No | Available | Stock status |
| createdById | string | Yes | - | ID of user who created the product |

**Indexes:**
- slug (unique)
- name
- createdById

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 3. categories
**Collection ID:** `categories`
**Purpose:** Store product categories

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| name | string | Yes | - | Category name |
| description | string | No | - | Category description |

**Indexes:**
- name (unique)

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 4. tags
**Collection ID:** `tags`
**Purpose:** Store product tags

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| name | string | Yes | - | Tag name |

**Indexes:**
- name (unique)

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 5. product_categories
**Collection ID:** `product_categories`
**Purpose:** Many-to-many relationship between products and categories

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| productId | string | Yes | - | Reference to product |
| categoryId | string | Yes | - | Reference to category |

**Indexes:**
- productId
- categoryId
- productId + categoryId (unique)

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 6. product_tags
**Collection ID:** `product_tags`
**Purpose:** Many-to-many relationship between products and tags

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| productId | string | Yes | - | Reference to product |
| tagId | string | Yes | - | Reference to tag |

**Indexes:**
- productId
- tagId
- productId + tagId (unique)

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 7. product_images (Enhanced Schema)
**Collection ID:** `product_images`
**Purpose:** Store product images with enhanced metadata

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| url | string | Yes | - | Image URL |
| alt | string | No | - | Alt text for accessibility |
| isFeatured | boolean | No | false | Whether image is featured |
| isMain | boolean | No | false | Whether this is the main product image |
| isPrimary | boolean | No | false | Whether this is the primary display image |
| order | integer | No | 0 | Display order (0 = first, 1 = second, etc.) |
| productId | string | Yes | - | Reference to product |

**Indexes:**
- productId
- isFeatured
- isMain
- order

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 8. specifications
**Collection ID:** `specifications`
**Purpose:** Store product specifications

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| key | string | Yes | - | Specification key (e.g., "origin") |
| value | string | Yes | - | Specification value (e.g., "Nigeria") |
| productId | string | Yes | - | Reference to product |

**Indexes:**
- productId
- key

**Permissions:**
- Read: any
- Write: admins
- Create: admins
- Update: admins
- Delete: admins

### 9. contact_submissions
**Collection ID:** `contact_submissions`
**Purpose:** Store contact form submissions

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| name | string | Yes | - | Contact name |
| email | string | Yes | - | Contact email |
| phone | string | No | - | Contact phone |
| company | string | No | - | Contact company |
| subject | string | No | - | Message subject |
| message | string | Yes | - | Message content |
| status | string | No | UNREAD | Status (UNREAD, READ, REPLIED, CLOSED) |

**Indexes:**
- email
- status
- $createdAt

**Permissions:**
- Read: admins
- Write: admins
- Create: any
- Update: admins
- Delete: admins

## Setup Instructions

1. **Create Database Collections:**
   - Log into Appwrite Console
   - Navigate to Database ID: `687e7c56003ba09479b1`
   - Create each collection with the specified fields and types
   - Set up indexes as specified
   - Configure permissions as outlined

2. **Initial Data Population:**
   - Use the provided setup scripts to populate initial data
   - Create admin user: <EMAIL> / admin123
   - Add sample categories and tags
   - Create sample products with enhanced image metadata

3. **Verification:**
   - Test API endpoints with the new schema
   - Verify product creation with multiple images
   - Confirm relationship data is properly stored
   - Test enhanced image functionality (main, primary, order)
