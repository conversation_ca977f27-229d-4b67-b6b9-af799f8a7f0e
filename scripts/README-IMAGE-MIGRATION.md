# Image Migration to Appwrite Storage

This document outlines the process for migrating product images from local file storage (`/public/uploads`) to Appwrite Storage as part of the broader Appwrite migration.

## Overview

The migration process involves:
1. **Scanning** existing images in `/public/uploads`
2. **Uploading** each image to Appwrite Storage
3. **Updating** database records to use new Appwrite Storage URLs
4. **Verifying** the migration was successful

## Prerequisites

Before running the migration, ensure:

1. **Appwrite Configuration**: All Appwrite environment variables are properly set in `.env`
2. **Database Access**: Prisma client can connect to the database
3. **File Permissions**: Read access to `/public/uploads` directory
4. **Appwrite Storage**: Storage bucket is created and accessible

## Environment Variables Required

```bash
NEXT_PUBLIC_APPWRITE_ENDPOINT=your-appwrite-endpoint
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your-bucket-id
```

## Migration Commands

### 1. Run Migration

```bash
npm run migrate:images migrate
```

This command will:
- Scan `/public/uploads` for image files
- Upload each image to Appwrite Storage
- Update ProductImage records in the database
- Provide progress updates and final summary

### 2. Verify Migration

```bash
npm run verify:images verify
```

This command will:
- Check all ProductImage records in the database
- Verify which images are migrated vs. still local
- Test accessibility of migrated images
- Provide detailed status report

### 3. Check Storage Bucket

```bash
npm run verify:images storage
```

This command will:
- List files in the Appwrite Storage bucket
- Show storage usage statistics
- Display recent uploads

### 4. Full Verification

```bash
npm run verify:images full
```

This command runs both storage check and migration verification.

### 5. Rollback Migration (if needed)

```bash
npm run migrate:images rollback
```

⚠️ **Use with caution**: This will:
- Delete files from Appwrite Storage
- Restore original local file URLs in database
- Should only be used if migration needs to be undone

## Migration Process Details

### File Processing

The migration script:
1. **Scans** `/public/uploads` for supported image formats (jpg, jpeg, png, gif, webp, svg)
2. **Validates** each file has corresponding database records
3. **Uploads** files to Appwrite Storage with proper permissions
4. **Updates** database URLs from `/uploads/filename.jpg` to Appwrite Storage URLs
5. **Logs** progress and handles errors gracefully

### URL Format Transformation

**Before Migration:**
```
/uploads/44fbf745-422a-46c0-87c2-bc914f5a61a4.jpg
```

**After Migration:**
```
https://your-endpoint/storage/buckets/bucket-id/files/file-id/view?project=project-id
```

### Error Handling

The migration script handles:
- **Missing files**: Logs warning if database references non-existent files
- **Upload failures**: Continues with other files, logs errors
- **Database errors**: Rolls back individual operations on failure
- **Network issues**: Includes retry logic and timeout handling

## Verification and Testing

### Post-Migration Checks

1. **Database Verification**: All ProductImage URLs should point to Appwrite Storage
2. **File Accessibility**: All migrated images should be accessible via new URLs
3. **Frontend Testing**: Verify images display correctly in the application
4. **Performance Testing**: Check image loading times and optimization

### Expected Results

After successful migration:
- ✅ All images accessible via Appwrite Storage URLs
- ✅ Database records updated with new URLs
- ✅ Original files preserved in `/public/uploads` (for backup)
- ✅ Image metadata (alt text, featured status) preserved

## Troubleshooting

### Common Issues

1. **Environment Variables Not Set**
   ```
   Error: NEXT_PUBLIC_APPWRITE_PROJECT_ID is not defined
   ```
   **Solution**: Verify all Appwrite environment variables are set

2. **Storage Bucket Not Found**
   ```
   Error: Bucket with the requested ID could not be found
   ```
   **Solution**: Create the storage bucket in Appwrite Console

3. **Permission Denied**
   ```
   Error: The current user is not authorized to perform the requested action
   ```
   **Solution**: Verify API key has proper permissions

4. **File Upload Failures**
   ```
   Error: Failed to upload file to Appwrite Storage
   ```
   **Solution**: Check file size limits and network connectivity

### Recovery Steps

If migration fails partially:

1. **Check Status**: Run `npm run verify:images verify`
2. **Identify Issues**: Review failed migrations in the output
3. **Fix Issues**: Address specific errors (permissions, network, etc.)
4. **Re-run Migration**: The script will skip already migrated files
5. **Verify Results**: Run verification again to confirm success

## File Structure

```
scripts/
├── migrate-images-to-appwrite.ts    # Main migration script
├── verify-image-migration.ts        # Verification script
└── README-IMAGE-MIGRATION.md        # This documentation

public/uploads/                       # Original image files (preserved)
├── image1.jpg
├── image2.png
└── ...
```

## Next Steps

After successful image migration:

1. **Test Application**: Verify all images display correctly
2. **Update Frontend**: Ensure components use new Appwrite Storage URLs
3. **Continue API Migration**: Proceed with migrating API routes to Appwrite
4. **Clean Up**: Consider removing old local files after verification period

## Security Considerations

- **File Permissions**: Migrated files have public read access
- **API Keys**: Ensure API keys are properly secured
- **Backup**: Original files are preserved for rollback capability
- **Access Control**: Consider implementing role-based access for sensitive images

## Performance Optimization

Post-migration optimizations:
- **Image Resizing**: Use Appwrite's built-in image transformation
- **CDN**: Leverage Appwrite's global CDN for faster delivery
- **Caching**: Implement proper caching headers
- **Lazy Loading**: Optimize frontend image loading

---

For questions or issues, refer to the Appwrite documentation or contact the development team.
