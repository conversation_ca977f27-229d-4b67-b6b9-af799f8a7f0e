require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const { readdir, readFile, stat } = require('fs').promises;
const { join, extname, basename } = require('path');
const path = require('path');

// Set the correct database path
process.env.DATABASE_URL = `file:${path.join(__dirname, '../prisma/dev.db')}`;

const prisma = new PrismaClient();

// Appwrite SDK
const sdk = require('node-appwrite');

// Initialize Appwrite client for server-side operations
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const storage = new sdk.Storage(client);

class ImageMigrationService {
  constructor() {
    this.uploadsDir = join(process.cwd(), 'public/uploads');
    this.supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    this.bucketId = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID;
    this.stats = {
      totalFiles: 0,
      successful: 0,
      failed: 0,
      results: []
    };
  }

  async migrate() {
    console.log('🚀 Starting image migration to Appwrite Storage...\n');

    try {
      // Step 1: Scan uploads directory
      const imageFiles = await this.scanUploadsDirectory();
      this.stats.totalFiles = imageFiles.length;

      if (imageFiles.length === 0) {
        console.log('ℹ️  No images found in uploads directory');
        return this.stats;
      }

      console.log(`📁 Found ${imageFiles.length} image files to migrate\n`);

      // Step 2: Process each image
      for (let i = 0; i < imageFiles.length; i++) {
        const filePath = imageFiles[i];
        const fileName = basename(filePath);
        
        console.log(`[${i + 1}/${imageFiles.length}] Processing: ${fileName}`);
        
        const result = await this.migrateImage(filePath);
        this.stats.results.push(result);

        if (result.success) {
          this.stats.successful++;
          console.log(`✅ Successfully migrated: ${fileName}`);
        } else {
          this.stats.failed++;
          console.log(`❌ Failed to migrate: ${fileName} - ${result.error}`);
        }

        // Add small delay to avoid overwhelming the API
        await this.delay(500);
      }

      // Step 3: Print summary
      this.printSummary();

      return this.stats;
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  async scanUploadsDirectory() {
    try {
      const files = await readdir(this.uploadsDir);
      const imageFiles = [];

      for (const file of files) {
        const filePath = join(this.uploadsDir, file);
        const fileStats = await stat(filePath);

        if (fileStats.isFile() && this.isImageFile(file)) {
          imageFiles.push(filePath);
        }
      }

      return imageFiles.sort();
    } catch (error) {
      console.error('Error scanning uploads directory:', error);
      return [];
    }
  }

  async migrateImage(filePath) {
    const fileName = basename(filePath);
    const originalPath = `/uploads/${fileName}`;

    try {
      // Step 1: Check if this image is referenced in the database
      const productImages = await prisma.productImage.findMany({
        where: {
          url: originalPath
        }
      });

      if (productImages.length === 0) {
        return {
          success: false,
          originalPath,
          error: 'No database records found for this image'
        };
      }

      // Step 2: Read the file
      const fileBuffer = await readFile(filePath);

      // Step 3: Prepare file for node-appwrite
      const fileId = sdk.ID.unique();

      // Step 4: Upload to Appwrite Storage using node-appwrite format
      const uploadResult = await storage.createFile(
        this.bucketId,
        fileId,
        sdk.InputFile.fromBuffer(fileBuffer, fileName),
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.write(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Step 5: Generate the new URL
      const newUrl = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${uploadResult.$id}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;

      // Step 6: Update database records
      await prisma.productImage.updateMany({
        where: {
          url: originalPath
        },
        data: {
          url: newUrl
        }
      });

      console.log(`   📝 Updated ${productImages.length} database record(s)`);

      return {
        success: true,
        originalPath,
        newUrl,
        fileId: uploadResult.$id
      };

    } catch (error) {
      console.error(`Error migrating ${fileName}:`, error);
      return {
        success: false,
        originalPath,
        error: error.message || 'Unknown error'
      };
    }
  }

  isImageFile(fileName) {
    const ext = extname(fileName).toLowerCase();
    return this.supportedExtensions.includes(ext);
  }

  getMimeType(fileName) {
    const ext = extname(fileName).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml'
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 MIGRATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total files processed: ${this.stats.totalFiles}`);
    console.log(`✅ Successful: ${this.stats.successful}`);
    console.log(`❌ Failed: ${this.stats.failed}`);
    console.log(`📈 Success rate: ${((this.stats.successful / this.stats.totalFiles) * 100).toFixed(1)}%`);

    if (this.stats.failed > 0) {
      console.log('\n❌ FAILED MIGRATIONS:');
      this.stats.results
        .filter(r => !r.success)
        .forEach(r => console.log(`   - ${r.originalPath}: ${r.error}`));
    }

    console.log('\n✨ Migration completed!');
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (command !== 'migrate') {
    console.log('Usage: node scripts/simple-migrate.js migrate');
    process.exit(1);
  }

  const migrationService = new ImageMigrationService();

  try {
    await migrationService.migrate();
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

main();
