require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const sdk = require('node-appwrite');
const path = require('path');

// Set the correct database path
process.env.DATABASE_URL = `file:${path.join(__dirname, '../prisma/dev.db')}`;

const prisma = new PrismaClient();

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || 'users',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || 'products',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || 'categories',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || 'tags',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || 'product_images',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || 'specifications',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || 'contact_submissions',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || 'product_categories',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || 'product_tags',
};

class DataMigrationService {
  constructor() {
    this.stats = {
      users: { migrated: 0, failed: 0 },
      categories: { migrated: 0, failed: 0 },
      tags: { migrated: 0, failed: 0 },
      products: { migrated: 0, failed: 0 },
      productImages: { migrated: 0, failed: 0 },
      specifications: { migrated: 0, failed: 0 },
      contactSubmissions: { migrated: 0, failed: 0 },
      relationships: { migrated: 0, failed: 0 }
    };
    this.idMappings = {
      users: new Map(),
      categories: new Map(),
      tags: new Map(),
      products: new Map()
    };
  }

  async migrateAllData() {
    console.log('🚀 Starting data migration from Prisma to Appwrite...\n');

    try {
      // Migrate in dependency order
      await this.migrateUsers();
      await this.migrateCategories();
      await this.migrateTags();
      await this.migrateProducts();
      await this.migrateProductImages();
      await this.migrateSpecifications();
      await this.migrateContactSubmissions();
      await this.migrateProductRelationships();

      this.printSummary();
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  async migrateUsers() {
    console.log('👤 Migrating Users...');
    try {
      const users = await prisma.user.findMany();
      console.log(`   Found ${users.length} users to migrate`);

      for (const user of users) {
        try {
          const appwriteUser = await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.USERS,
            sdk.ID.unique(),
            {
              name: user.name,
              email: user.email,
              password: user.password,
              role: user.role
            }
          );

          this.idMappings.users.set(user.id, appwriteUser.$id);
          this.stats.users.migrated++;
          console.log(`   ✅ Migrated user: ${user.email}`);
        } catch (error) {
          this.stats.users.failed++;
          console.log(`   ❌ Failed to migrate user ${user.email}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate users:', error.message);
    }
  }

  async migrateCategories() {
    console.log('📂 Migrating Categories...');
    try {
      const categories = await prisma.category.findMany();
      console.log(`   Found ${categories.length} categories to migrate`);

      for (const category of categories) {
        try {
          const appwriteCategory = await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.CATEGORIES,
            sdk.ID.unique(),
            {
              name: category.name,
              description: category.description || ''
            }
          );

          this.idMappings.categories.set(category.id, appwriteCategory.$id);
          this.stats.categories.migrated++;
          console.log(`   ✅ Migrated category: ${category.name}`);
        } catch (error) {
          this.stats.categories.failed++;
          console.log(`   ❌ Failed to migrate category ${category.name}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate categories:', error.message);
    }
  }

  async migrateTags() {
    console.log('🏷️  Migrating Tags...');
    try {
      const tags = await prisma.tag.findMany();
      console.log(`   Found ${tags.length} tags to migrate`);

      for (const tag of tags) {
        try {
          const appwriteTag = await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.TAGS,
            sdk.ID.unique(),
            {
              name: tag.name
            }
          );

          this.idMappings.tags.set(tag.id, appwriteTag.$id);
          this.stats.tags.migrated++;
          console.log(`   ✅ Migrated tag: ${tag.name}`);
        } catch (error) {
          this.stats.tags.failed++;
          console.log(`   ❌ Failed to migrate tag ${tag.name}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate tags:', error.message);
    }
  }

  async migrateProducts() {
    console.log('📦 Migrating Products...');
    try {
      const products = await prisma.product.findMany();
      console.log(`   Found ${products.length} products to migrate`);

      for (const product of products) {
        try {
          const createdById = this.idMappings.users.get(product.createdById);
          if (!createdById) {
            console.log(`   ⚠️  Skipping product ${product.name}: creator not found`);
            this.stats.products.failed++;
            continue;
          }

          const appwriteProduct = await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.PRODUCTS,
            sdk.ID.unique(),
            {
              name: product.name,
              slug: product.slug,
              description: product.description,
              details: product.details || '',
              price: product.price || '',
              stock: product.stock,
              createdById: createdById
            }
          );

          this.idMappings.products.set(product.id, appwriteProduct.$id);
          this.stats.products.migrated++;
          console.log(`   ✅ Migrated product: ${product.name}`);
        } catch (error) {
          this.stats.products.failed++;
          console.log(`   ❌ Failed to migrate product ${product.name}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate products:', error.message);
    }
  }

  async migrateProductImages() {
    console.log('🖼️  Migrating Product Images...');
    try {
      const images = await prisma.productImage.findMany();
      console.log(`   Found ${images.length} product images to migrate`);

      for (const image of images) {
        try {
          const productId = this.idMappings.products.get(image.productId);
          if (!productId) {
            console.log(`   ⚠️  Skipping image: product not found`);
            this.stats.productImages.failed++;
            continue;
          }

          await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.PRODUCT_IMAGES,
            sdk.ID.unique(),
            {
              url: image.url,
              alt: image.alt || '',
              isFeatured: image.isFeatured,
              productId: productId
            }
          );

          this.stats.productImages.migrated++;
          console.log(`   ✅ Migrated image: ${image.url.split('/').pop()}`);
        } catch (error) {
          this.stats.productImages.failed++;
          console.log(`   ❌ Failed to migrate image: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate product images:', error.message);
    }
  }

  async migrateSpecifications() {
    console.log('📋 Migrating Specifications...');
    try {
      const specifications = await prisma.specification.findMany();
      console.log(`   Found ${specifications.length} specifications to migrate`);

      for (const spec of specifications) {
        try {
          const productId = this.idMappings.products.get(spec.productId);
          if (!productId) {
            console.log(`   ⚠️  Skipping specification: product not found`);
            this.stats.specifications.failed++;
            continue;
          }

          await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.SPECIFICATIONS,
            sdk.ID.unique(),
            {
              key: spec.key,
              value: JSON.stringify(spec.value),
              productId: productId
            }
          );

          this.stats.specifications.migrated++;
          console.log(`   ✅ Migrated specification: ${spec.key}`);
        } catch (error) {
          this.stats.specifications.failed++;
          console.log(`   ❌ Failed to migrate specification ${spec.key}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate specifications:', error.message);
    }
  }

  async migrateContactSubmissions() {
    console.log('📧 Migrating Contact Submissions...');
    try {
      const submissions = await prisma.contactSubmission.findMany();
      console.log(`   Found ${submissions.length} contact submissions to migrate`);

      for (const submission of submissions) {
        try {
          await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.CONTACT_SUBMISSIONS,
            sdk.ID.unique(),
            {
              name: submission.name,
              email: submission.email,
              phone: submission.phone || '',
              company: submission.company || '',
              subject: submission.subject || '',
              message: submission.message,
              status: submission.status
            }
          );

          this.stats.contactSubmissions.migrated++;
          console.log(`   ✅ Migrated contact submission from: ${submission.email}`);
        } catch (error) {
          this.stats.contactSubmissions.failed++;
          console.log(`   ❌ Failed to migrate contact submission: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to migrate contact submissions:', error.message);
    }
  }

  async migrateProductRelationships() {
    console.log('🔗 Migrating Product Relationships...');
    try {
      // Migrate product-category relationships
      const products = await prisma.product.findMany({
        include: {
          categories: true,
          tags: true
        }
      });

      for (const product of products) {
        const appwriteProductId = this.idMappings.products.get(product.id);
        if (!appwriteProductId) continue;

        // Migrate categories
        for (const category of product.categories) {
          const appwriteCategoryId = this.idMappings.categories.get(category.id);
          if (appwriteCategoryId) {
            try {
              await databases.createDocument(
                DATABASE_ID,
                COLLECTIONS.PRODUCT_CATEGORIES,
                sdk.ID.unique(),
                {
                  productId: appwriteProductId,
                  categoryId: appwriteCategoryId
                }
              );
              this.stats.relationships.migrated++;
            } catch (error) {
              this.stats.relationships.failed++;
              console.log(`   ❌ Failed to create product-category relationship: ${error.message}`);
            }
          }
        }

        // Migrate tags
        for (const tag of product.tags) {
          const appwriteTagId = this.idMappings.tags.get(tag.id);
          if (appwriteTagId) {
            try {
              await databases.createDocument(
                DATABASE_ID,
                COLLECTIONS.PRODUCT_TAGS,
                sdk.ID.unique(),
                {
                  productId: appwriteProductId,
                  tagId: appwriteTagId
                }
              );
              this.stats.relationships.migrated++;
            } catch (error) {
              this.stats.relationships.failed++;
              console.log(`   ❌ Failed to create product-tag relationship: ${error.message}`);
            }
          }
        }
      }

      console.log(`   ✅ Migrated ${this.stats.relationships.migrated} relationships`);
    } catch (error) {
      console.error('❌ Failed to migrate relationships:', error.message);
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 DATA MIGRATION SUMMARY');
    console.log('='.repeat(60));

    const categories = [
      'users', 'categories', 'tags', 'products', 
      'productImages', 'specifications', 'contactSubmissions', 'relationships'
    ];

    let totalMigrated = 0;
    let totalFailed = 0;

    categories.forEach(category => {
      const stats = this.stats[category];
      totalMigrated += stats.migrated;
      totalFailed += stats.failed;
      
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      console.log(`${categoryName}: ✅ ${stats.migrated} migrated, ❌ ${stats.failed} failed`);
    });

    console.log('\n' + '='.repeat(60));
    console.log(`📈 TOTAL: ✅ ${totalMigrated} migrated, ❌ ${totalFailed} failed`);
    
    const successRate = totalMigrated + totalFailed > 0 ? 
      (totalMigrated / (totalMigrated + totalFailed)) * 100 : 0;
    console.log(`📊 Success Rate: ${successRate.toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 All data migrated successfully!');
    } else {
      console.log('\n⚠️  Some data failed to migrate. Check the logs above for details.');
    }
  }
}

async function main() {
  const migrationService = new DataMigrationService();
  try {
    await migrationService.migrateAllData();
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

main();
