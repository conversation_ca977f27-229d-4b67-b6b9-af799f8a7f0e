#!/usr/bin/env ts-node

import { readdir, readFile, stat } from 'fs/promises';
import { join, extname, basename } from 'path';
import { PrismaClient } from '@prisma/client';
import { AppwriteServerStorageService, FileHelpers } from '../src/lib/appwrite-storage.js';
import { createServerClient, STORAGE_BUCKET_ID } from '../src/lib/appwrite.js';

// Initialize services
const prisma = new PrismaClient();

interface MigrationResult {
  success: boolean;
  originalPath: string;
  newUrl?: string;
  fileId?: string;
  error?: string;
}

interface MigrationStats {
  totalFiles: number;
  successful: number;
  failed: number;
  skipped: number;
  results: MigrationResult[];
}

class ImageMigrationService {
  private stats: MigrationStats = {
    totalFiles: 0,
    successful: 0,
    failed: 0,
    skipped: 0,
    results: []
  };

  private readonly uploadsDir = join(process.cwd(), 'public/uploads');
  private readonly supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];

  /**
   * Main migration function
   */
  async migrate(): Promise<MigrationStats> {
    console.log('🚀 Starting image migration to Appwrite Storage...\n');

    try {
      // Step 1: Scan uploads directory
      const imageFiles = await this.scanUploadsDirectory();
      this.stats.totalFiles = imageFiles.length;

      if (imageFiles.length === 0) {
        console.log('ℹ️  No images found in uploads directory');
        return this.stats;
      }

      console.log(`📁 Found ${imageFiles.length} image files to migrate\n`);

      // Step 2: Process each image
      for (let i = 0; i < imageFiles.length; i++) {
        const filePath = imageFiles[i];
        const fileName = basename(filePath);
        
        console.log(`[${i + 1}/${imageFiles.length}] Processing: ${fileName}`);
        
        const result = await this.migrateImage(filePath);
        this.stats.results.push(result);

        if (result.success) {
          this.stats.successful++;
          console.log(`✅ Successfully migrated: ${fileName}`);
        } else {
          this.stats.failed++;
          console.log(`❌ Failed to migrate: ${fileName} - ${result.error}`);
        }

        // Add small delay to avoid overwhelming the API
        await this.delay(100);
      }

      // Step 3: Print summary
      this.printSummary();

      return this.stats;
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Scan uploads directory for image files
   */
  private async scanUploadsDirectory(): Promise<string[]> {
    try {
      const files = await readdir(this.uploadsDir);
      const imageFiles: string[] = [];

      for (const file of files) {
        const filePath = join(this.uploadsDir, file);
        const fileStats = await stat(filePath);

        if (fileStats.isFile() && this.isImageFile(file)) {
          imageFiles.push(filePath);
        }
      }

      return imageFiles.sort();
    } catch (error) {
      console.error('Error scanning uploads directory:', error);
      return [];
    }
  }

  /**
   * Migrate a single image file
   */
  private async migrateImage(filePath: string): Promise<MigrationResult> {
    const fileName = basename(filePath);
    const originalPath = `/uploads/${fileName}`;

    try {
      // Step 1: Check if this image is referenced in the database
      const productImages = await prisma.productImage.findMany({
        where: {
          url: originalPath
        }
      });

      if (productImages.length === 0) {
        return {
          success: false,
          originalPath,
          error: 'No database records found for this image'
        };
      }

      // Step 2: Read the file
      const fileBuffer = await readFile(filePath);
      const mimeType = this.getMimeType(fileName);

      // Step 3: Upload to Appwrite Storage
      const uploadResult = await AppwriteServerStorageService.uploadFileFromServer(
        fileBuffer,
        fileName,
        mimeType
      );

      // Step 4: Generate the new URL
      const newUrl = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${STORAGE_BUCKET_ID}/files/${uploadResult.$id}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;

      // Step 5: Update database records
      await prisma.productImage.updateMany({
        where: {
          url: originalPath
        },
        data: {
          url: newUrl
        }
      });

      console.log(`   📝 Updated ${productImages.length} database record(s)`);

      return {
        success: true,
        originalPath,
        newUrl,
        fileId: uploadResult.$id
      };

    } catch (error) {
      console.error(`Error migrating ${fileName}:`, error);
      return {
        success: false,
        originalPath,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if file is an image based on extension
   */
  private isImageFile(fileName: string): boolean {
    const ext = extname(fileName).toLowerCase();
    return this.supportedExtensions.includes(ext);
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeType(fileName: string): string {
    const ext = extname(fileName).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml'
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * Add delay between operations
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Print migration summary
   */
  private printSummary(): void {
    console.log('\n' + '='.repeat(50));
    console.log('📊 MIGRATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total files processed: ${this.stats.totalFiles}`);
    console.log(`✅ Successful: ${this.stats.successful}`);
    console.log(`❌ Failed: ${this.stats.failed}`);
    console.log(`⏭️  Skipped: ${this.stats.skipped}`);
    console.log(`📈 Success rate: ${((this.stats.successful / this.stats.totalFiles) * 100).toFixed(1)}%`);

    if (this.stats.failed > 0) {
      console.log('\n❌ FAILED MIGRATIONS:');
      this.stats.results
        .filter(r => !r.success)
        .forEach(r => console.log(`   - ${r.originalPath}: ${r.error}`));
    }

    console.log('\n✨ Migration completed!');
  }

  /**
   * Rollback migration (restore original URLs)
   */
  async rollback(): Promise<void> {
    console.log('🔄 Starting rollback process...\n');

    try {
      // Get all product images with Appwrite URLs
      const appwriteImages = await prisma.productImage.findMany({
        where: {
          url: {
            contains: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || ''
          }
        }
      });

      console.log(`Found ${appwriteImages.length} images to rollback`);

      for (const image of appwriteImages) {
        try {
          // Extract file ID from Appwrite URL
          const fileId = FileHelpers.extractFileIdFromUrl(image.url);
          if (!fileId) continue;

          // Try to delete from Appwrite Storage
          await AppwriteServerStorageService.deleteFileFromServer(fileId);

          // Restore original URL (assuming the filename is preserved)
          const fileName = image.url.split('/').pop()?.split('?')[0];
          if (fileName) {
            await prisma.productImage.update({
              where: { id: image.id },
              data: { url: `/uploads/${fileName}` }
            });
            console.log(`✅ Rolled back: ${fileName}`);
          }
        } catch (error) {
          console.error(`❌ Failed to rollback image ${image.id}:`, error);
        }
      }

      console.log('\n✨ Rollback completed!');
    } catch (error) {
      console.error('💥 Rollback failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const migrationService = new ImageMigrationService();

  try {
    switch (command) {
      case 'migrate':
        await migrationService.migrate();
        break;
      case 'rollback':
        await migrationService.rollback();
        break;
      default:
        console.log('Usage:');
        console.log('  npm run migrate:images migrate   - Migrate images to Appwrite');
        console.log('  npm run migrate:images rollback  - Rollback migration');
        process.exit(1);
    }
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { ImageMigrationService };
