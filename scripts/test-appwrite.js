require('dotenv').config();
const sdk = require('node-appwrite');

const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const storage = new sdk.Storage(client);
const databases = new sdk.Databases(client);
const ID = sdk.ID; // Ensure ID is imported

console.log('Database methods:', Object.keys(storage));

async function testAppwriteConnection() {
  console.log('🔍 Testing Appwrite connection...\n');

  console.log('Configuration:');
  console.log(`Endpoint: ${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}`);
  console.log(`Project ID: ${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`);
  console.log(`Database ID: ${process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID}`);
  console.log(`Storage Bucket ID: ${process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID}`);
  console.log(`API Key: ${process.env.APPWRITE_API_KEY ? 'Set' : 'Not set'}\n`);

  // Test 1: Check if we can list files in the bucket
  try {
    console.log('📁 Testing file listing in bucket...');
    const files = await storage.listFiles(process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID);
    console.log(`✅ Bucket contains ${files.total} files`);

    if (files.files && files.files.length > 0) {
      console.log('Files in bucket:');
      files.files.slice(0, 3).forEach(file => {
        console.log(`   - ${file.name} (${(file.sizeOriginal / 1024).toFixed(1)} KB)`);
      });
    }
  } catch (error) {
    console.log('❌ File listing test failed:', error.message);
    console.log('Full error:', error);
  }

  // Test 2: Try to upload a real image file
  try {
    console.log('\n📤 Testing image file upload...');
    const imagePath = '/home/<USER>/Desktop/Helevon/web apps/clients/afric-source/public/assets/agriculture-6749210_1280.jpg';
    
   const file = sdk.InputFile.fromPath(imagePath, 'file.jpg');
    
    const result = await storage.createFile(
      process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID,
      ID.unique(),
      file, // Use the file stream
      
    );
    console.log(`✅ Image file uploaded successfully: ${result.$id}`);

    // Clean up - delete the test file
    try {
      await storage.deleteFile(process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID, result.$id);
      console.log('✅ Test file cleaned up');
    } catch (cleanupError) {
      console.log('⚠️  Could not clean up test file:', cleanupError.message);
    }
  } catch (error) {
    console.log('❌ Image file upload test failed:', error.message);
    console.log('Full error:', error);
  }

  // Test 3: Check database access by listing collections
  try {
    console.log('\n🗄️  Testing database access...');
    const collections = await databases.listCollections(
      process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID
    );

    console.log(`✅ Found ${collections.total} collections in database`);

    if (collections.collections && collections.collections.length > 0) {
      console.log('Available collections:');
      collections.collections.forEach(collection => {
        console.log(`   - ${collection.name} (ID: ${collection.$id})`);
      });
    }
  } catch (error) {
    console.log('❌ Database test failed:', error.message);
    console.log('Full error:', error);
  }

  console.log('\n✨ Connection test completed!');
}

testAppwriteConnection().catch(console.error);
