require('dotenv').config();
const sdk = require('node-appwrite');

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

// Expected collections
const EXPECTED_COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || 'users',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || 'products',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || 'categories',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || 'tags',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || 'product_images',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || 'specifications',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || 'contact_submissions',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || 'product_categories',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || 'product_tags',
};

class AppwriteCollectionVerifier {
  constructor() {
    this.results = {
      existing: [],
      missing: [],
      errors: []
    };
  }

  async verifyAllCollections() {
    console.log('🔍 Verifying Appwrite Collections...\n');
    console.log(`Database ID: ${DATABASE_ID}\n`);

    try {
      // Get all collections in the database
      const collections = await databases.listCollections(DATABASE_ID);
      console.log(`📊 Found ${collections.total} collections in database\n`);

      // Check each expected collection
      for (const [name, id] of Object.entries(EXPECTED_COLLECTIONS)) {
        await this.verifyCollection(name, id, collections.collections);
      }

      this.printSummary();
      return this.results;
    } catch (error) {
      console.error('💥 Verification failed:', error);
      throw error;
    }
  }

  async verifyCollection(name, id, allCollections) {
    try {
      console.log(`🔍 Checking ${name} collection (${id})...`);
      
      // Check if collection exists
      const collection = allCollections.find(c => c.$id === id);
      
      if (!collection) {
        console.log(`❌ Collection ${name} (${id}) not found`);
        this.results.missing.push({ name, id });
        return;
      }

      // Get collection details
      const collectionDetails = await databases.getCollection(DATABASE_ID, id);
      
      // Get attributes
      const attributes = await databases.listAttributes(DATABASE_ID, id);
      
      // Get indexes
      const indexes = await databases.listIndexes(DATABASE_ID, id);

      console.log(`✅ Collection ${name} exists`);
      console.log(`   - Attributes: ${attributes.total}`);
      console.log(`   - Indexes: ${indexes.total}`);
      console.log(`   - Documents: ${collectionDetails.documentsCount || 0}`);

      this.results.existing.push({
        name,
        id,
        attributes: attributes.total,
        indexes: indexes.total,
        documents: collectionDetails.documentsCount || 0,
        details: {
          attributes: attributes.attributes.map(attr => ({
            key: attr.key,
            type: attr.type,
            required: attr.required,
            array: attr.array
          })),
          indexes: indexes.indexes.map(idx => ({
            key: idx.key,
            type: idx.type,
            attributes: idx.attributes
          }))
        }
      });

    } catch (error) {
      console.log(`❌ Error checking ${name}: ${error.message}`);
      this.results.errors.push({ name, id, error: error.message });
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 APPWRITE COLLECTIONS VERIFICATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Existing collections: ${this.results.existing.length}`);
    console.log(`❌ Missing collections: ${this.results.missing.length}`);
    console.log(`⚠️  Errors: ${this.results.errors.length}`);

    if (this.results.existing.length > 0) {
      console.log('\n✅ EXISTING COLLECTIONS:');
      this.results.existing.forEach(collection => {
        console.log(`   - ${collection.name} (${collection.id})`);
        console.log(`     Attributes: ${collection.attributes}, Indexes: ${collection.indexes}, Documents: ${collection.documents}`);
      });
    }

    if (this.results.missing.length > 0) {
      console.log('\n❌ MISSING COLLECTIONS:');
      this.results.missing.forEach(collection => {
        console.log(`   - ${collection.name} (${collection.id})`);
      });
      console.log('\n💡 Run "npm run setup:collections" to create missing collections');
    }

    if (this.results.errors.length > 0) {
      console.log('\n⚠️  ERRORS:');
      this.results.errors.forEach(error => {
        console.log(`   - ${error.name}: ${error.error}`);
      });
    }

    // Overall status
    const totalExpected = Object.keys(EXPECTED_COLLECTIONS).length;
    const successRate = (this.results.existing.length / totalExpected) * 100;
    
    console.log(`\n📈 Success Rate: ${successRate.toFixed(1)}% (${this.results.existing.length}/${totalExpected})`);
    
    if (successRate === 100) {
      console.log('\n🎉 All collections are set up correctly!');
    } else {
      console.log('\n⚠️  Some collections are missing or have errors.');
    }
  }

  async printDetailedInfo() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 DETAILED COLLECTION INFORMATION');
    console.log('='.repeat(60));

    for (const collection of this.results.existing) {
      console.log(`\n📦 ${collection.name} (${collection.id})`);
      console.log('   Attributes:');
      collection.details.attributes.forEach(attr => {
        const required = attr.required ? '(required)' : '(optional)';
        const array = attr.array ? '[]' : '';
        console.log(`     - ${attr.key}: ${attr.type}${array} ${required}`);
      });
      
      if (collection.details.indexes.length > 0) {
        console.log('   Indexes:');
        collection.details.indexes.forEach(idx => {
          console.log(`     - ${idx.key} (${idx.type}): [${idx.attributes.join(', ')}]`);
        });
      }
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const verifier = new AppwriteCollectionVerifier();

  try {
    const results = await verifier.verifyAllCollections();
    
    if (command === 'detailed' || command === 'detail') {
      await verifier.printDetailedInfo();
    }

    // Exit with error code if there are missing collections or errors
    if (results.missing.length > 0 || results.errors.length > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error('Verification failed:', error);
    process.exit(1);
  }
}

main();
