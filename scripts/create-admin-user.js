require('dotenv').config();
const sdk = require('node-appwrite');
const { hash } = require('bcrypt');

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;
const USERS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || 'users';

class AdminUserManager {
  async checkExistingUsers() {
    console.log('🔍 Checking existing users...');
    try {
      const users = await databases.listDocuments(DATABASE_ID, USERS_COLLECTION_ID);
      console.log(`📊 Found ${users.total} users in database`);
      
      if (users.total > 0) {
        console.log('\n👥 Existing users:');
        users.documents.forEach(user => {
          console.log(`   - ${user.email} (${user.role || 'USER'}) - ID: ${user.$id}`);
        });
      }
      
      return users.documents;
    } catch (error) {
      console.error('❌ Error checking users:', error.message);
      return [];
    }
  }

  async createAdminUser(email = '<EMAIL>', password = 'admin123', name = 'Admin User') {
    console.log(`\n🔐 Creating admin user: ${email}`);
    
    try {
      // Check if user already exists
      const existingUsers = await this.checkExistingUsers();
      const existingUser = existingUsers.find(user => user.email === email);
      
      if (existingUser) {
        console.log(`⚠️  User ${email} already exists with ID: ${existingUser.$id}`);
        console.log(`   Role: ${existingUser.role || 'USER'}`);
        
        if (existingUser.role !== 'ADMIN') {
          console.log('🔄 Updating user role to ADMIN...');
          const updatedUser = await databases.updateDocument(
            DATABASE_ID,
            USERS_COLLECTION_ID,
            existingUser.$id,
            { role: 'ADMIN' }
          );
          console.log('✅ User role updated to ADMIN');
          return updatedUser;
        }
        
        return existingUser;
      }

      // Hash the password
      const hashedPassword = await hash(password, 12);

      // Create new admin user
      const adminUser = await databases.createDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        sdk.ID.unique(),
        {
          name: name,
          email: email,
          password: hashedPassword,
          role: 'ADMIN'
        }
      );

      console.log('✅ Admin user created successfully!');
      console.log(`   ID: ${adminUser.$id}`);
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Role: ${adminUser.role}`);
      
      return adminUser;
    } catch (error) {
      console.error('❌ Error creating admin user:', error.message);
      throw error;
    }
  }

  async resetAdminPassword(email = '<EMAIL>', newPassword = 'admin123') {
    console.log(`\n🔄 Resetting password for: ${email}`);
    
    try {
      const existingUsers = await this.checkExistingUsers();
      const user = existingUsers.find(u => u.email === email);
      
      if (!user) {
        console.log(`❌ User ${email} not found`);
        return null;
      }

      // Hash the new password
      const hashedPassword = await hash(newPassword, 12);

      // Update user password
      const updatedUser = await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        user.$id,
        {
          password: hashedPassword,
          role: 'ADMIN' // Ensure admin role
        }
      );

      console.log('✅ Password reset successfully!');
      console.log(`   Email: ${updatedUser.email}`);
      console.log(`   Role: ${updatedUser.role}`);
      
      return updatedUser;
    } catch (error) {
      console.error('❌ Error resetting password:', error.message);
      throw error;
    }
  }

  printCredentials(email = '<EMAIL>', password = 'admin123') {
    console.log('\n' + '='.repeat(60));
    console.log('🔑 ADMIN LOGIN CREDENTIALS');
    console.log('='.repeat(60));
    console.log(`📧 Email: ${email}`);
    console.log(`🔐 Password: ${password}`);
    console.log(`🌐 Dashboard URL: http://localhost:3000/dashboard`);
    console.log('='.repeat(60));
    console.log('\n💡 Use these credentials to log into the admin dashboard');
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'create';
  const email = args[1] || '<EMAIL>';
  const password = args[2] || 'admin123';

  const adminManager = new AdminUserManager();

  try {
    switch (command) {
      case 'check':
        await adminManager.checkExistingUsers();
        break;
        
      case 'create':
        await adminManager.createAdminUser(email, password);
        adminManager.printCredentials(email, password);
        break;
        
      case 'reset':
        await adminManager.resetAdminPassword(email, password);
        adminManager.printCredentials(email, password);
        break;
        
      default:
        console.log('Usage:');
        console.log('  node create-admin-user.js check');
        console.log('  node create-admin-user.js create [email] [password]');
        console.log('  node create-admin-user.js reset [email] [password]');
        break;
    }
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  }
}

main();
