require('dotenv').config();
const fs = require('fs');
const path = require('path');
const sdk = require('node-appwrite');

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const storage = new sdk.Storage(client);

const ASSETS_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_ASSETS_BUCKET_ID;
const ASSETS_DIR = path.join(__dirname, '../public/assets');

class StaticAssetsMigrator {
  constructor() {
    this.migrationResults = {
      successful: [],
      failed: [],
      skipped: []
    };
    this.urlMappings = new Map();
  }

  async migrateAllAssets() {
    console.log('🚀 Starting static assets migration to Appwrite Storage...\n');
    console.log(`📁 Source directory: ${ASSETS_DIR}`);
    console.log(`🪣 Target bucket: ${ASSETS_BUCKET_ID}\n`);

    try {
      // Check if assets directory exists
      if (!fs.existsSync(ASSETS_DIR)) {
        throw new Error(`Assets directory not found: ${ASSETS_DIR}`);
      }

      // Get all files in assets directory
      const files = this.getAllFiles(ASSETS_DIR);
      console.log(`📊 Found ${files.length} files to migrate\n`);

      // Migrate each file
      for (let i = 0; i < files.length; i++) {
        const filePath = files[i];
        const relativePath = path.relative(ASSETS_DIR, filePath);
        
        console.log(`📤 [${i + 1}/${files.length}] Migrating: ${relativePath}`);
        
        try {
          const result = await this.migrateFile(filePath, relativePath);
          if (result) {
            this.migrationResults.successful.push({
              originalPath: relativePath,
              newUrl: result.url,
              fileId: result.fileId,
              size: result.size
            });
            console.log(`   ✅ Success: ${result.url}`);
          }
        } catch (error) {
          this.migrationResults.failed.push({
            originalPath: relativePath,
            error: error.message
          });
          console.log(`   ❌ Failed: ${error.message}`);
        }
      }

      await this.generateUrlMappingFile();
      this.printSummary();
      
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    }
  }

  getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
      const fullPath = path.join(dirPath, file);
      if (fs.statSync(fullPath).isDirectory()) {
        arrayOfFiles = this.getAllFiles(fullPath, arrayOfFiles);
      } else {
        arrayOfFiles.push(fullPath);
      }
    });

    return arrayOfFiles;
  }

  async migrateFile(filePath, relativePath) {
    try {
      // Read file
      const fileBuffer = fs.readFileSync(filePath);
      const stats = fs.statSync(filePath);
      
      // Get file info
      const fileName = path.basename(filePath);
      const fileExtension = path.extname(fileName).toLowerCase();
      const mimeType = this.getMimeType(fileExtension);

      // Create a unique file ID based on the relative path
      const fileId = this.createFileId(relativePath);

      // Check if file already exists
      try {
        await storage.getFile(ASSETS_BUCKET_ID, fileId);
        console.log(`   ⚠️  File already exists, skipping: ${relativePath}`);
        
        // Still generate URL for existing file
        const url = this.getFileUrl(fileId);
        this.urlMappings.set(`/assets/${relativePath}`, url);
        
        this.migrationResults.skipped.push({
          originalPath: relativePath,
          existingUrl: url,
          fileId: fileId
        });
        
        return {
          url: url,
          fileId: fileId,
          size: stats.size
        };
      } catch (error) {
        // File doesn't exist, proceed with upload
      }

      // Upload file to Appwrite Storage
      const file = await storage.createFile(
        ASSETS_BUCKET_ID,
        fileId,
        sdk.InputFile.fromBuffer(fileBuffer, fileName)
      );

      // Generate URL
      const url = this.getFileUrl(file.$id);
      
      // Store URL mapping
      this.urlMappings.set(`/assets/${relativePath}`, url);

      return {
        url: url,
        fileId: file.$id,
        size: file.sizeOriginal || stats.size
      };

    } catch (error) {
      throw new Error(`Failed to migrate ${relativePath}: ${error.message}`);
    }
  }

  createFileId(relativePath) {
    // Create a safe file ID from the relative path
    let fileId = relativePath
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();

    // If the file ID is too long, create a hash-based ID
    if (fileId.length > 36) {
      const crypto = require('crypto');
      const hash = crypto.createHash('md5').update(relativePath).digest('hex').substring(0, 8);
      const extension = path.extname(relativePath).toLowerCase();
      const baseName = path.basename(relativePath, extension).substring(0, 20);
      fileId = `${baseName}_${hash}${extension}`.replace(/[^a-zA-Z0-9._-]/g, '_');
    }

    return fileId;
  }

  getMimeType(extension) {
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.css': 'text/css',
      '.js': 'application/javascript'
    };
    return mimeTypes[extension] || 'application/octet-stream';
  }

  getFileUrl(fileId) {
    return `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${ASSETS_BUCKET_ID}/files/${fileId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;
  }

  async generateUrlMappingFile() {
    console.log('\n📝 Generating URL mapping file...');
    
    const mappingData = {
      migrationDate: new Date().toISOString(),
      totalFiles: this.migrationResults.successful.length + this.migrationResults.skipped.length,
      bucketId: ASSETS_BUCKET_ID,
      mappings: Object.fromEntries(this.urlMappings)
    };

    const mappingFilePath = path.join(__dirname, 'asset-url-mappings.json');
    fs.writeFileSync(mappingFilePath, JSON.stringify(mappingData, null, 2));
    
    console.log(`✅ URL mappings saved to: ${mappingFilePath}`);
  }

  printSummary() {
    console.log('\n' + '='.repeat(70));
    console.log('📊 STATIC ASSETS MIGRATION SUMMARY');
    console.log('='.repeat(70));
    
    const total = this.migrationResults.successful.length + 
                  this.migrationResults.failed.length + 
                  this.migrationResults.skipped.length;
    
    console.log(`📁 Total files processed: ${total}`);
    console.log(`✅ Successfully migrated: ${this.migrationResults.successful.length}`);
    console.log(`⚠️  Skipped (already exists): ${this.migrationResults.skipped.length}`);
    console.log(`❌ Failed: ${this.migrationResults.failed.length}`);
    
    if (this.migrationResults.successful.length > 0) {
      console.log('\n✅ SUCCESSFULLY MIGRATED FILES:');
      this.migrationResults.successful.forEach(file => {
        console.log(`   - ${file.originalPath} → ${file.newUrl}`);
      });
    }

    if (this.migrationResults.skipped.length > 0) {
      console.log('\n⚠️  SKIPPED FILES (already exist):');
      this.migrationResults.skipped.forEach(file => {
        console.log(`   - ${file.originalPath} → ${file.existingUrl}`);
      });
    }

    if (this.migrationResults.failed.length > 0) {
      console.log('\n❌ FAILED FILES:');
      this.migrationResults.failed.forEach(file => {
        console.log(`   - ${file.originalPath}: ${file.error}`);
      });
    }

    const successRate = total > 0 ? 
      ((this.migrationResults.successful.length + this.migrationResults.skipped.length) / total) * 100 : 0;
    
    console.log(`\n📈 Success Rate: ${successRate.toFixed(1)}%`);
    
    if (this.migrationResults.failed.length === 0) {
      console.log('\n🎉 All assets migrated successfully!');
      console.log('\n📝 Next steps:');
      console.log('   1. Update code references to use new Appwrite Storage URLs');
      console.log('   2. Test all images and assets are loading correctly');
      console.log('   3. Remove local /public/assets/ directory after verification');
    } else {
      console.log('\n⚠️  Some assets failed to migrate. Check the errors above.');
    }
    
    console.log('\n🔗 URL mappings saved to: scripts/asset-url-mappings.json');
  }
}

async function main() {
  const migrator = new StaticAssetsMigrator();
  
  try {
    await migrator.migrateAllAssets();
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

main();
