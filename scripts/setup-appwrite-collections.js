require('dotenv').config();
const sdk = require('node-appwrite');

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

// Collection IDs from environment
const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || 'users',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || 'products',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || 'categories',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || 'tags',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || 'product_images',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || 'specifications',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || 'contact_submissions',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || 'product_categories',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || 'product_tags',
};

class AppwriteCollectionSetup {
  constructor() {
    this.createdCollections = [];
    this.errors = [];
  }

  async setupAllCollections() {
    console.log('🚀 Starting Appwrite Collections Setup...\n');
    console.log(`Database ID: ${DATABASE_ID}\n`);

    try {
      // Create collections in order (dependencies first)
      await this.createUsersCollection();
      await this.createCategoriesCollection();
      await this.createTagsCollection();
      await this.createProductsCollection();
      await this.createProductImagesCollection();
      await this.createSpecificationsCollection();
      await this.createContactSubmissionsCollection();
      await this.createProductCategoriesCollection();
      await this.createProductTagsCollection();

      this.printSummary();
    } catch (error) {
      console.error('💥 Setup failed:', error);
      throw error;
    }
  }

  async createUsersCollection() {
    console.log('👤 Creating Users collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.USERS,
        'Users',
        [
          sdk.Permission.read(sdk.Role.users()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USERS, 'name', 255, true);
      await databases.createEmailAttribute(DATABASE_ID, COLLECTIONS.USERS, 'email', true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USERS, 'password', 255, true);
      await databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.USERS, 'role', ['USER', 'ADMIN'], false, 'USER');

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.USERS, 'email_index', 'unique', ['email']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.USERS, 'role_index', 'key', ['role']);

      console.log('✅ Users collection created successfully');
      this.createdCollections.push('Users');
    } catch (error) {
      console.error('❌ Failed to create Users collection:', error.message);
      this.errors.push(`Users: ${error.message}`);
    }
  }

  async createCategoriesCollection() {
    console.log('📂 Creating Categories collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        'Categories',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, 'name', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, 'description', 1000, false);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CATEGORIES, 'name_index', 'unique', ['name']);

      console.log('✅ Categories collection created successfully');
      this.createdCollections.push('Categories');
    } catch (error) {
      console.error('❌ Failed to create Categories collection:', error.message);
      this.errors.push(`Categories: ${error.message}`);
    }
  }

  async createTagsCollection() {
    console.log('🏷️  Creating Tags collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        'Tags',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.TAGS, 'name', 255, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.TAGS, 'name_index', 'unique', ['name']);

      console.log('✅ Tags collection created successfully');
      this.createdCollections.push('Tags');
    } catch (error) {
      console.error('❌ Failed to create Tags collection:', error.message);
      this.errors.push(`Tags: ${error.message}`);
    }
  }

  async createProductsCollection() {
    console.log('📦 Creating Products collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        'Products',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'name', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'slug', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'description', 2000, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'details', 5000, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'price', 100, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'stock', 100, false, 'Available');
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'createdById', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'slug_index', 'unique', ['slug']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'name_index', 'key', ['name']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'createdBy_index', 'key', ['createdById']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'created_at_index', 'key', ['$createdAt']);

      console.log('✅ Products collection created successfully');
      this.createdCollections.push('Products');
    } catch (error) {
      console.error('❌ Failed to create Products collection:', error.message);
      this.errors.push(`Products: ${error.message}`);
    }
  }

  async createProductImagesCollection() {
    console.log('🖼️  Creating Product Images collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_IMAGES,
        'Product Images',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'url', 500, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'alt', 255, false);
      await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'isFeatured', false, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'productId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'featured_index', 'key', ['isFeatured']);

      console.log('✅ Product Images collection created successfully');
      this.createdCollections.push('Product Images');
    } catch (error) {
      console.error('❌ Failed to create Product Images collection:', error.message);
      this.errors.push(`Product Images: ${error.message}`);
    }
  }

  async createSpecificationsCollection() {
    console.log('📋 Creating Specifications collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.SPECIFICATIONS,
        'Specifications',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'key', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'value', 2000, true); // JSON as string
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'productId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'key_index', 'key', ['key']);

      console.log('✅ Specifications collection created successfully');
      this.createdCollections.push('Specifications');
    } catch (error) {
      console.error('❌ Failed to create Specifications collection:', error.message);
      this.errors.push(`Specifications: ${error.message}`);
    }
  }

  async createContactSubmissionsCollection() {
    console.log('📧 Creating Contact Submissions collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        'Contact Submissions',
        [
          sdk.Permission.read(sdk.Role.users()),
          sdk.Permission.create(sdk.Role.any()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'name', 255, true);
      await databases.createEmailAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'email', true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'phone', 50, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'company', 255, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'subject', 255, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'message', 2000, true);
      await databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'status', ['UNREAD', 'READ', 'REPLIED', 'ARCHIVED'], false, 'UNREAD');

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'status_index', 'key', ['status']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'email_index', 'key', ['email']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'created_at_index', 'key', ['$createdAt']);

      console.log('✅ Contact Submissions collection created successfully');
      this.createdCollections.push('Contact Submissions');
    } catch (error) {
      console.error('❌ Failed to create Contact Submissions collection:', error.message);
      this.errors.push(`Contact Submissions: ${error.message}`);
    }
  }

  async createProductCategoriesCollection() {
    console.log('🔗 Creating Product-Categories relationship collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_CATEGORIES,
        'Product Categories',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'productId', 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'categoryId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'category_index', 'key', ['categoryId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'unique_relation', 'unique', ['productId', 'categoryId']);

      console.log('✅ Product-Categories collection created successfully');
      this.createdCollections.push('Product Categories');
    } catch (error) {
      console.error('❌ Failed to create Product-Categories collection:', error.message);
      this.errors.push(`Product Categories: ${error.message}`);
    }
  }

  async createProductTagsCollection() {
    console.log('🔗 Creating Product-Tags relationship collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_TAGS,
        'Product Tags',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'productId', 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'tagId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'tag_index', 'key', ['tagId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'unique_relation', 'unique', ['productId', 'tagId']);

      console.log('✅ Product-Tags collection created successfully');
      this.createdCollections.push('Product Tags');
    } catch (error) {
      console.error('❌ Failed to create Product-Tags collection:', error.message);
      this.errors.push(`Product Tags: ${error.message}`);
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 APPWRITE COLLECTIONS SETUP SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Successfully created: ${this.createdCollections.length} collections`);
    console.log(`❌ Failed: ${this.errors.length} collections`);

    if (this.createdCollections.length > 0) {
      console.log('\n✅ CREATED COLLECTIONS:');
      this.createdCollections.forEach(collection => {
        console.log(`   - ${collection}`);
      });
    }

    if (this.errors.length > 0) {
      console.log('\n❌ FAILED COLLECTIONS:');
      this.errors.forEach(error => {
        console.log(`   - ${error}`);
      });
    }

    console.log('\n🎉 Setup completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Verify collections in Appwrite Console');
    console.log('   2. Test API endpoints');
    console.log('   3. Run data migration if needed');
  }
}

async function main() {
  const setup = new AppwriteCollectionSetup();
  try {
    await setup.setupAllCollections();
  } catch (error) {
    console.error('Setup failed:', error);
    process.exit(1);
  }
}

main();
