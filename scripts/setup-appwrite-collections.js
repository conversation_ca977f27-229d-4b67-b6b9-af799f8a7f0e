require('dotenv').config();
const sdk = require('node-appwrite');

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);
const users = new sdk.Users(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

// Collection IDs from environment
const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || 'users',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || 'products',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || 'categories',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || 'tags',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || 'product_images',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || 'specifications',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || 'contact_submissions',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || 'product_categories',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || 'product_tags',
};

class AppwriteCollectionSetup {
  constructor() {
    this.createdCollections = [];
    this.errors = [];
  }

  async setupAllCollections() {
    console.log('🚀 Starting Appwrite Collections Setup...\n');
    console.log(`Database ID: ${DATABASE_ID}\n`);

    try {
      // Create collections in order (dependencies first)
      await this.createUsersCollection();
      await this.createCategoriesCollection();
      await this.createTagsCollection();
      await this.createProductsCollection();
      await this.createProductImagesCollection();
      await this.createSpecificationsCollection();
      await this.createContactSubmissionsCollection();
      await this.createProductCategoriesCollection();
      await this.createProductTagsCollection();

      // Wait a moment for collections to be fully ready
      console.log('\n⏳ Waiting for collections to be ready...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Populate initial data
      await this.populateInitialData();

      this.printSummary();
    } catch (error) {
      console.error('💥 Setup failed:', error);
      throw error;
    }
  }

  async createUsersCollection() {
    console.log('👤 Creating Users collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.USERS,
        'Users',
        [
          sdk.Permission.read(sdk.Role.users()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USERS, 'name', 255, true);
      await databases.createEmailAttribute(DATABASE_ID, COLLECTIONS.USERS, 'email', true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USERS, 'password', 255, true);
      await databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.USERS, 'role', ['USER', 'ADMIN'], false, 'USER');

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.USERS, 'email_index', 'unique', ['email']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.USERS, 'role_index', 'key', ['role']);

      console.log('✅ Users collection created successfully');
      this.createdCollections.push('Users');
    } catch (error) {
      console.error('❌ Failed to create Users collection:', error.message);
      this.errors.push(`Users: ${error.message}`);
    }
  }

  async createCategoriesCollection() {
    console.log('📂 Creating Categories collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        'Categories',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, 'name', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, 'description', 1000, false);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CATEGORIES, 'name_index', 'unique', ['name']);

      console.log('✅ Categories collection created successfully');
      this.createdCollections.push('Categories');
    } catch (error) {
      console.error('❌ Failed to create Categories collection:', error.message);
      this.errors.push(`Categories: ${error.message}`);
    }
  }

  async createTagsCollection() {
    console.log('🏷️  Creating Tags collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.TAGS,
        'Tags',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.TAGS, 'name', 255, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.TAGS, 'name_index', 'unique', ['name']);

      console.log('✅ Tags collection created successfully');
      this.createdCollections.push('Tags');
    } catch (error) {
      console.error('❌ Failed to create Tags collection:', error.message);
      this.errors.push(`Tags: ${error.message}`);
    }
  }

  async createProductsCollection() {
    console.log('📦 Creating Products collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        'Products',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'name', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'slug', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'description', 2000, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'details', 5000, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'price', 100, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'stock', 100, false, 'Available');
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'createdById', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'slug_index', 'unique', ['slug']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'name_index', 'key', ['name']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'createdBy_index', 'key', ['createdById']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'created_at_index', 'key', ['$createdAt']);

      console.log('✅ Products collection created successfully');
      this.createdCollections.push('Products');
    } catch (error) {
      console.error('❌ Failed to create Products collection:', error.message);
      this.errors.push(`Products: ${error.message}`);
    }
  }

  async createProductImagesCollection() {
    console.log('🖼️  Creating Product Images collection (Enhanced Schema)...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_IMAGES,
        'Product Images',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes (Enhanced Schema)
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'url', 500, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'alt', 255, false);
      await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'isFeatured', false, false);
      await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'isMain', false, false);
      await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'isPrimary', false, false);
      await databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'order', false, 0);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'productId', 50, true);

      // Create indexes (Enhanced)
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'featured_index', 'key', ['isFeatured']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'main_index', 'key', ['isMain']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'order_index', 'key', ['order']);

      console.log('✅ Product Images collection (Enhanced) created successfully');
      this.createdCollections.push('Product Images (Enhanced)');
    } catch (error) {
      console.error('❌ Failed to create Product Images collection:', error.message);
      this.errors.push(`Product Images: ${error.message}`);
    }
  }

  async createSpecificationsCollection() {
    console.log('📋 Creating Specifications collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.SPECIFICATIONS,
        'Specifications',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'key', 255, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'value', 2000, true); // JSON as string
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'productId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.SPECIFICATIONS, 'key_index', 'key', ['key']);

      console.log('✅ Specifications collection created successfully');
      this.createdCollections.push('Specifications');
    } catch (error) {
      console.error('❌ Failed to create Specifications collection:', error.message);
      this.errors.push(`Specifications: ${error.message}`);
    }
  }

  async createContactSubmissionsCollection() {
    console.log('📧 Creating Contact Submissions collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        'Contact Submissions',
        [
          sdk.Permission.read(sdk.Role.users()),
          sdk.Permission.create(sdk.Role.any()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'name', 255, true);
      await databases.createEmailAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'email', true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'phone', 50, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'company', 255, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'subject', 255, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'message', 2000, true);
      await databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'status', ['UNREAD', 'READ', 'REPLIED', 'ARCHIVED'], false, 'UNREAD');

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'status_index', 'key', ['status']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'email_index', 'key', ['email']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'created_at_index', 'key', ['$createdAt']);

      console.log('✅ Contact Submissions collection created successfully');
      this.createdCollections.push('Contact Submissions');
    } catch (error) {
      console.error('❌ Failed to create Contact Submissions collection:', error.message);
      this.errors.push(`Contact Submissions: ${error.message}`);
    }
  }

  async createProductCategoriesCollection() {
    console.log('🔗 Creating Product-Categories relationship collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_CATEGORIES,
        'Product Categories',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'productId', 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'categoryId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'category_index', 'key', ['categoryId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_CATEGORIES, 'unique_relation', 'unique', ['productId', 'categoryId']);

      console.log('✅ Product-Categories collection created successfully');
      this.createdCollections.push('Product Categories');
    } catch (error) {
      console.error('❌ Failed to create Product-Categories collection:', error.message);
      this.errors.push(`Product Categories: ${error.message}`);
    }
  }

  async createProductTagsCollection() {
    console.log('🔗 Creating Product-Tags relationship collection...');
    try {
      const collection = await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.PRODUCT_TAGS,
        'Product Tags',
        [
          sdk.Permission.read(sdk.Role.any()),
          sdk.Permission.create(sdk.Role.users()),
          sdk.Permission.update(sdk.Role.users()),
          sdk.Permission.delete(sdk.Role.users())
        ]
      );

      // Add attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'productId', 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'tagId', 50, true);

      // Create indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'tag_index', 'key', ['tagId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_TAGS, 'unique_relation', 'unique', ['productId', 'tagId']);

      console.log('✅ Product-Tags collection created successfully');
      this.createdCollections.push('Product Tags');
    } catch (error) {
      console.error('❌ Failed to create Product-Tags collection:', error.message);
      this.errors.push(`Product Tags: ${error.message}`);
    }
  }

  async populateInitialData() {
    console.log('\n🌱 Populating initial data...');

    try {
      // Create admin user first
      await this.createAdminUser();

      // Create sample categories
      const categories = await this.createSampleCategories();

      // Create sample tags
      const tags = await this.createSampleTags();

      // Create sample products with enhanced images
      await this.createSampleProducts(categories, tags);

      // Create sample contact submission
      await this.createSampleContact();

      console.log('✅ Initial data population completed');

    } catch (error) {
      console.error('❌ Failed to populate initial data:', error);
      this.errors.push(`Data Population: ${error.message}`);
    }
  }

  async createAdminUser() {
    console.log('👤 Creating admin user...');

    try {
      // Create admin user in Appwrite Auth
      const adminUser = await users.create(
        sdk.ID.unique(),
        '<EMAIL>',
        undefined, // phone
        'admin123', // password
        'Admin User' // name
      );

      console.log(`✅ Admin user created: ${adminUser.$id}`);

      // Create user document in users collection
      await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        adminUser.$id,
        {
          name: 'Admin User',
          email: '<EMAIL>',
          password: 'admin123', // This will be hashed by Appwrite
          role: 'ADMIN'
        }
      );

      console.log('✅ Admin user document created');

    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️  Admin user already exists');
      } else {
        console.error('❌ Error creating admin user:', error.message);
        throw error;
      }
    }
  }

  async createSampleCategories() {
    console.log('📂 Creating sample categories...');

    const categoryData = [
      { name: 'Grains & Seeds', description: 'Cereals, seeds, and grain products from across Africa' },
      { name: 'Nuts & Kernels', description: 'Premium nuts and kernels including cashews, groundnuts' },
      { name: 'Oils & Fats', description: 'Natural oils and fats including shea butter, palm oil' },
      { name: 'Beans & Legumes', description: 'Various beans, peas, and other legumes' },
      { name: 'Spices & Herbs', description: 'Aromatic spices, herbs, and seasonings' },
      { name: 'Fruits & Vegetables', description: 'Fresh and dried fruits and vegetables' }
    ];

    const createdCategories = [];

    for (const category of categoryData) {
      try {
        const doc = await databases.createDocument(
          DATABASE_ID,
          COLLECTIONS.CATEGORIES,
          sdk.ID.unique(),
          category
        );
        console.log(`  ✅ Created category: ${category.name}`);
        createdCategories.push(doc);
      } catch (error) {
        console.error(`  ❌ Failed to create category ${category.name}:`, error.message);
      }
    }

    return createdCategories;
  }

  async createSampleTags() {
    console.log('🏷️  Creating sample tags...');

    const tagNames = [
      'Organic',
      'Fair Trade',
      'Non-GMO',
      'Sustainable',
      'Premium',
      'Bulk Available',
      'Export Quality',
      'Certified'
    ];

    const createdTags = [];

    for (const tagName of tagNames) {
      try {
        const doc = await databases.createDocument(
          DATABASE_ID,
          COLLECTIONS.TAGS,
          sdk.ID.unique(),
          { name: tagName }
        );
        console.log(`  ✅ Created tag: ${tagName}`);
        createdTags.push(doc);
      } catch (error) {
        console.error(`  ❌ Failed to create tag ${tagName}:`, error.message);
      }
    }

    return createdTags;
  }

  async createSampleProducts(categories, tags) {
    console.log('📦 Creating sample products with enhanced images...');

    if (categories.length === 0 || tags.length === 0) {
      console.log('⚠️  Skipping product creation - no categories or tags available');
      return;
    }

    const sampleProducts = [
      {
        name: 'Premium Sesame Seeds',
        slug: 'premium-sesame-seeds',
        description: 'High-quality sesame seeds sourced from the finest farms in Nigeria. Rich in nutrients and perfect for various culinary applications.',
        details: 'Our premium sesame seeds are carefully selected from organic farms in Northern Nigeria. These seeds are known for their exceptional quality, rich flavor, and high oil content. Perfect for tahini production, baking, and cooking. Each batch is thoroughly cleaned and tested for purity.',
        price: '$2,500 per metric ton',
        stock: 'Available',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
            alt: 'Premium sesame seeds in bulk',
            isFeatured: true,
            isMain: true,
            isPrimary: true,
            order: 0
          },
          {
            url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600',
            alt: 'Close-up of sesame seeds showing quality',
            isFeatured: false,
            isMain: false,
            isPrimary: false,
            order: 1
          },
          {
            url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400',
            alt: 'Sesame seeds packaging',
            isFeatured: false,
            isMain: false,
            isPrimary: false,
            order: 2
          }
        ],
        specifications: [
          { key: 'Origin', value: 'Nigeria' },
          { key: 'Purity', value: '99.5%' },
          { key: 'Moisture Content', value: 'Max 6%' },
          { key: 'Oil Content', value: '48-52%' },
          { key: 'Packaging', value: '25kg, 50kg bags' }
        ],
        categoryIds: [categories[0].$id], // Grains & Seeds
        tagIds: [tags[0].$id, tags[3].$id, tags[6].$id] // Organic, Sustainable, Export Quality
      },
      {
        name: 'Organic Cashew Nuts',
        slug: 'organic-cashew-nuts',
        description: 'Premium organic cashew nuts from West Africa. Carefully processed to maintain natural flavor and nutritional value.',
        details: 'Our organic cashew nuts are sourced from certified organic farms across West Africa. These cashews are processed using traditional methods to preserve their natural taste and nutritional benefits. Available in various grades and sizes to meet different market requirements.',
        price: '$8,500 per metric ton',
        stock: 'Available',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=800',
            alt: 'Organic cashew nuts premium quality',
            isFeatured: true,
            isMain: true,
            isPrimary: true,
            order: 0
          },
          {
            url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=600',
            alt: 'Cashew nuts different grades',
            isFeatured: false,
            isMain: false,
            isPrimary: false,
            order: 1
          }
        ],
        specifications: [
          { key: 'Origin', value: 'West Africa' },
          { key: 'Grade', value: 'W240, W320, W450' },
          { key: 'Moisture Content', value: 'Max 5%' },
          { key: 'Broken Nuts', value: 'Max 5%' },
          { key: 'Certification', value: 'Organic, Fair Trade' }
        ],
        categoryIds: [categories[1].$id], // Nuts & Kernels
        tagIds: [tags[0].$id, tags[1].$id, tags[4].$id, tags[7].$id] // Organic, Fair Trade, Premium, Certified
      }
    ];

    for (const productData of sampleProducts) {
      try {
        // Create the main product
        const product = await databases.createDocument(
          DATABASE_ID,
          COLLECTIONS.PRODUCTS,
          sdk.ID.unique(),
          {
            name: productData.name,
            slug: productData.slug,
            description: productData.description,
            details: productData.details,
            price: productData.price,
            stock: productData.stock,
            createdById: 'admin'
          }
        );

        console.log(`  ✅ Created product: ${product.name}`);

        // Create product images with enhanced schema
        for (const imageData of productData.images) {
          try {
            await databases.createDocument(
              DATABASE_ID,
              COLLECTIONS.PRODUCT_IMAGES,
              sdk.ID.unique(),
              {
                ...imageData,
                productId: product.$id
              }
            );
            console.log(`    ✅ Added image: ${imageData.alt}`);
          } catch (error) {
            console.error(`    ❌ Error creating image:`, error.message);
          }
        }

        // Create specifications
        for (const spec of productData.specifications) {
          try {
            await databases.createDocument(
              DATABASE_ID,
              COLLECTIONS.SPECIFICATIONS,
              sdk.ID.unique(),
              {
                ...spec,
                productId: product.$id
              }
            );
            console.log(`    ✅ Added specification: ${spec.key}`);
          } catch (error) {
            console.error(`    ❌ Error creating specification:`, error.message);
          }
        }

        // Create category relationships
        for (const categoryId of productData.categoryIds) {
          try {
            await databases.createDocument(
              DATABASE_ID,
              COLLECTIONS.PRODUCT_CATEGORIES,
              sdk.ID.unique(),
              {
                productId: product.$id,
                categoryId: categoryId
              }
            );
            console.log(`    ✅ Added category relationship`);
          } catch (error) {
            console.error(`    ❌ Error creating category relationship:`, error.message);
          }
        }

        // Create tag relationships
        for (const tagId of productData.tagIds) {
          try {
            await databases.createDocument(
              DATABASE_ID,
              COLLECTIONS.PRODUCT_TAGS,
              sdk.ID.unique(),
              {
                productId: product.$id,
                tagId: tagId
              }
            );
            console.log(`    ✅ Added tag relationship`);
          } catch (error) {
            console.error(`    ❌ Error creating tag relationship:`, error.message);
          }
        }

      } catch (error) {
        console.error(`  ❌ Error creating product ${productData.name}:`, error.message);
      }
    }
  }

  async createSampleContact() {
    console.log('📧 Creating sample contact submission...');

    try {
      const contact = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        sdk.ID.unique(),
        {
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '******-0123',
          company: 'Global Trading Co.',
          subject: 'Inquiry about Premium Sesame Seeds',
          message: 'Hello, I am interested in purchasing premium sesame seeds for our European market. Could you please provide more details about pricing, minimum order quantities, and shipping terms? We are looking for organic certified products.',
          status: 'UNREAD'
        }
      );
      console.log(`  ✅ Created sample contact: ${contact.$id}`);
    } catch (error) {
      console.error('  ❌ Error creating sample contact:', error.message);
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 APPWRITE COLLECTIONS SETUP SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Successfully created: ${this.createdCollections.length} collections`);
    console.log(`❌ Failed: ${this.errors.length} collections`);

    if (this.createdCollections.length > 0) {
      console.log('\n✅ CREATED COLLECTIONS:');
      this.createdCollections.forEach(collection => {
        console.log(`   - ${collection}`);
      });
    }

    if (this.errors.length > 0) {
      console.log('\n❌ FAILED COLLECTIONS:');
      this.errors.forEach(error => {
        console.log(`   - ${error}`);
      });
    }

    console.log('\n🎉 Setup completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Verify collections in Appwrite Console');
    console.log('   2. Test API endpoints');
    console.log('   3. Run data migration if needed');
  }
}

async function main() {
  const setup = new AppwriteCollectionSetup();
  try {
    await setup.setupAllCollections();
  } catch (error) {
    console.error('Setup failed:', error);
    process.exit(1);
  }
}

main();
