# Appwrite Collections Setup Guide

This guide covers the complete setup of Appwrite collections for the AfricSource application, including collection creation, data migration, and verification.

## Overview

The application uses 9 Appwrite collections that mirror the original Prisma schema:

### Core Collections
1. **Users** - User accounts and authentication
2. **Products** - Product catalog
3. **Categories** - Product categories
4. **Tags** - Product tags
5. **Product Images** - Product image references
6. **Specifications** - Product specifications (JSON data)
7. **Contact Submissions** - Contact form submissions

### Relationship Collections
8. **Product Categories** - Many-to-many product-category relationships
9. **Product Tags** - Many-to-many product-tag relationships

## Prerequisites

Before running the setup scripts, ensure:

1. **Appwrite Server**: Running and accessible
2. **Environment Variables**: All Appwrite variables are set in `.env`
3. **Database Created**: Appwrite database exists with correct ID
4. **Storage Bucket**: Created for product images
5. **API Key**: Has proper permissions for database operations

## Required Environment Variables

```bash
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=http://your-appwrite-endpoint/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your-database-id

# Collection IDs
NEXT_PUBLIC_APPWRITE_COLLECTION_USERS=users
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS=products
NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES=categories
NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS=tags
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES=product_images
NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS=specifications
NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS=contact_submissions
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES=product_categories
NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS=product_tags

# Storage
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your-bucket-id
```

## Setup Commands

### 1. Verify Current Status

Check which collections already exist:

```bash
npm run verify:collections
```

For detailed information about existing collections:

```bash
npm run verify:collections detailed
```

### 2. Create Collections

Create all required collections with proper attributes and indexes:

```bash
npm run setup:collections
```

This will create:
- All 9 collections with proper schemas
- Required attributes for each collection
- Indexes for performance optimization
- Proper permissions for each collection

### 3. Migrate Existing Data

If you have existing data in Prisma/SQLite, migrate it to Appwrite:

```bash
npm run migrate:data
```

This will:
- Migrate all users, categories, tags, products
- Migrate product images, specifications, contact submissions
- Create proper relationships between products and categories/tags
- Maintain referential integrity with ID mappings

### 4. Verify Setup

After setup and migration, verify everything is working:

```bash
npm run verify:collections
```

## Collection Schemas

### Users Collection
```
- name: string (required)
- email: email (required, unique)
- password: string (required)
- role: enum ['USER', 'ADMIN'] (required, default: 'USER')
```

### Products Collection
```
- name: string (required)
- slug: string (required, unique)
- description: string (required)
- details: string (optional)
- price: string (optional)
- stock: string (required, default: 'Available')
- createdById: string (required, references Users)
```

### Categories Collection
```
- name: string (required, unique)
- description: string (optional)
```

### Tags Collection
```
- name: string (required, unique)
```

### Product Images Collection
```
- url: string (required)
- alt: string (optional)
- isFeatured: boolean (required, default: false)
- productId: string (required, references Products)
```

### Specifications Collection
```
- key: string (required)
- value: string (required, JSON data)
- productId: string (required, references Products)
```

### Contact Submissions Collection
```
- name: string (required)
- email: email (required)
- phone: string (optional)
- company: string (optional)
- subject: string (optional)
- message: string (required)
- status: enum ['UNREAD', 'READ', 'REPLIED', 'ARCHIVED'] (required, default: 'UNREAD')
```

### Product Categories Collection (Relationship)
```
- productId: string (required, references Products)
- categoryId: string (required, references Categories)
- Unique constraint: (productId, categoryId)
```

### Product Tags Collection (Relationship)
```
- productId: string (required, references Products)
- tagId: string (required, references Tags)
- Unique constraint: (productId, tagId)
```

## Permissions

Each collection has appropriate permissions:

- **Public Read**: Categories, Tags, Products, Product Images, Specifications
- **Authenticated Write**: All collections (users can create/update)
- **Admin Only**: User management, Contact Submissions management
- **Public Create**: Contact Submissions (for contact form)

## Indexes

Performance indexes are created for:

- **Unique Constraints**: email, slug, name (where applicable)
- **Foreign Keys**: productId, categoryId, tagId, createdById
- **Query Optimization**: status, role, createdAt, isFeatured
- **Relationship Constraints**: Unique combinations for many-to-many tables

## Troubleshooting

### Common Issues

1. **Collection Already Exists**
   ```
   Error: Collection with the requested ID already exists
   ```
   **Solution**: Collections exist, run verification instead

2. **Permission Denied**
   ```
   Error: The current user is not authorized
   ```
   **Solution**: Check API key permissions in Appwrite Console

3. **Database Not Found**
   ```
   Error: Database with the requested ID could not be found
   ```
   **Solution**: Create database in Appwrite Console first

4. **Attribute Creation Failed**
   ```
   Error: Attribute with the requested key already exists
   ```
   **Solution**: Collection partially created, may need manual cleanup

### Recovery Steps

If setup fails partially:

1. **Check Status**: `npm run verify:collections detailed`
2. **Manual Cleanup**: Delete partially created collections in Appwrite Console
3. **Retry Setup**: `npm run setup:collections`
4. **Verify Results**: `npm run verify:collections`

## Next Steps

After successful setup:

1. **Test API Endpoints**: Verify CRUD operations work
2. **Update Frontend**: Ensure components use new Appwrite APIs
3. **Test Dashboard**: Verify admin functionality works
4. **Performance Testing**: Check query performance with indexes
5. **Backup Strategy**: Set up regular backups for production

## Monitoring

Monitor collection health:

- **Document Counts**: Track growth over time
- **Query Performance**: Monitor slow queries
- **Storage Usage**: Track file storage consumption
- **API Usage**: Monitor request patterns and limits

---

For questions or issues, refer to the Appwrite documentation or contact the development team.
