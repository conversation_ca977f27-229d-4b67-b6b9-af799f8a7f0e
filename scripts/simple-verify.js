require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const path = require('path');

// Set the correct database path
process.env.DATABASE_URL = `file:${path.join(__dirname, '../prisma/dev.db')}`;

const prisma = new PrismaClient();

async function verifyImages() {
  console.log('🔍 Verifying image migration status...\n');

  try {
    // Get all product images
    const productImages = await prisma.productImage.findMany({
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📊 Found ${productImages.length} product images in database\n`);

    let migratedCount = 0;
    let localCount = 0;
    let brokenCount = 0;

    // Check each image
    for (const image of productImages) {
      if (image.url.includes(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')) {
        migratedCount++;
        console.log(`✅ Migrated: ${image.url.split('/').pop()}`);
      } else if (image.url.startsWith('/uploads/')) {
        localCount++;
        console.log(`📁 Local file: ${image.url}`);
      } else {
        brokenCount++;
        console.log(`❓ Unknown URL format: ${image.url}`);
      }
    }

    // Print summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total images: ${productImages.length}`);
    console.log(`✅ Migrated to Appwrite: ${migratedCount} (${((migratedCount / productImages.length) * 100).toFixed(1)}%)`);
    console.log(`📁 Still local: ${localCount} (${((localCount / productImages.length) * 100).toFixed(1)}%)`);
    console.log(`❌ Broken links: ${brokenCount} (${((brokenCount / productImages.length) * 100).toFixed(1)}%)`);

    if (migratedCount === productImages.length) {
      console.log('\n🎉 All images have been successfully migrated!');
    } else if (migratedCount > 0) {
      console.log('\n⚠️  Migration is partially complete.');
      console.log(`   ${localCount} images still need to be migrated.`);
    } else {
      console.log('\n❌ No images have been migrated yet.');
    }

  } catch (error) {
    console.error('💥 Verification failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyImages();
