require('dotenv').config();
const sdk = require('node-appwrite');

// Initialize Appwrite client
const client = new sdk.Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new sdk.Databases(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || 'users',
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCTS || 'products',
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CATEGORIES || 'categories',
  TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TAGS || 'tags',
  PRODUCT_IMAGES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_IMAGES || 'product_images',
  SPECIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SPECIFICATIONS || 'specifications',
  CONTACT_SUBMISSIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACT_SUBMISSIONS || 'contact_submissions',
  PRODUCT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_CATEGORIES || 'product_categories',
  PRODUCT_TAGS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRODUCT_TAGS || 'product_tags',
};

class AppwriteCollectionFixer {
  constructor() {
    this.fixed = [];
    this.errors = [];
  }

  async fixAllCollections() {
    console.log('🔧 Fixing Appwrite Collections...\n');

    try {
      await this.fixUsersCollection();
      await this.fixProductsCollection();
      await this.fixProductImagesCollection();
      await this.fixContactSubmissionsCollection();

      this.printSummary();
    } catch (error) {
      console.error('💥 Fix failed:', error);
      throw error;
    }
  }

  async fixUsersCollection() {
    console.log('👤 Fixing Users collection...');
    try {
      // Add missing role attribute
      await databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.USERS, 'role', ['USER', 'ADMIN'], false, 'USER');
      console.log('   ✅ Added role attribute');

      // Add missing indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.USERS, 'email_index', 'unique', ['email']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.USERS, 'role_index', 'key', ['role']);
      console.log('   ✅ Added indexes');

      this.fixed.push('Users');
    } catch (error) {
      console.error('   ❌ Failed to fix Users collection:', error.message);
      this.errors.push(`Users: ${error.message}`);
    }
  }

  async fixProductsCollection() {
    console.log('📦 Fixing Products collection...');
    try {
      // Add missing attributes
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'stock', 100, false, 'Available');
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCTS, 'createdById', 50, true);
      console.log('   ✅ Added missing attributes');

      // Add missing indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'slug_index', 'unique', ['slug']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'name_index', 'key', ['name']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'createdBy_index', 'key', ['createdById']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCTS, 'created_at_index', 'key', ['$createdAt']);
      console.log('   ✅ Added indexes');

      this.fixed.push('Products');
    } catch (error) {
      console.error('   ❌ Failed to fix Products collection:', error.message);
      this.errors.push(`Products: ${error.message}`);
    }
  }

  async fixProductImagesCollection() {
    console.log('🖼️  Fixing Product Images collection...');
    try {
      // Add missing attributes
      await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'isFeatured', false, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'productId', 50, true);
      console.log('   ✅ Added missing attributes');

      // Add missing indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'product_index', 'key', ['productId']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.PRODUCT_IMAGES, 'featured_index', 'key', ['isFeatured']);
      console.log('   ✅ Added indexes');

      this.fixed.push('Product Images');
    } catch (error) {
      console.error('   ❌ Failed to fix Product Images collection:', error.message);
      this.errors.push(`Product Images: ${error.message}`);
    }
  }

  async fixContactSubmissionsCollection() {
    console.log('📧 Fixing Contact Submissions collection...');
    try {
      // Add missing status attribute
      await databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'status', ['UNREAD', 'READ', 'REPLIED', 'ARCHIVED'], false, 'UNREAD');
      console.log('   ✅ Added status attribute');

      // Add missing indexes
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'status_index', 'key', ['status']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'email_index', 'key', ['email']);
      await databases.createIndex(DATABASE_ID, COLLECTIONS.CONTACT_SUBMISSIONS, 'created_at_index', 'key', ['$createdAt']);
      console.log('   ✅ Added indexes');

      this.fixed.push('Contact Submissions');
    } catch (error) {
      console.error('   ❌ Failed to fix Contact Submissions collection:', error.message);
      this.errors.push(`Contact Submissions: ${error.message}`);
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 APPWRITE COLLECTIONS FIX SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Successfully fixed: ${this.fixed.length} collections`);
    console.log(`❌ Failed: ${this.errors.length} collections`);

    if (this.fixed.length > 0) {
      console.log('\n✅ FIXED COLLECTIONS:');
      this.fixed.forEach(collection => {
        console.log(`   - ${collection}`);
      });
    }

    if (this.errors.length > 0) {
      console.log('\n❌ FAILED TO FIX:');
      this.errors.forEach(error => {
        console.log(`   - ${error}`);
      });
    }

    console.log('\n🎉 Fix completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run verification: npm run verify:collections');
    console.log('   2. Test API endpoints');
    console.log('   3. Run data migration: npm run migrate:data');
  }
}

async function main() {
  const fixer = new AppwriteCollectionFixer();
  try {
    await fixer.fixAllCollections();
  } catch (error) {
    console.error('Fix failed:', error);
    process.exit(1);
  }
}

main();
