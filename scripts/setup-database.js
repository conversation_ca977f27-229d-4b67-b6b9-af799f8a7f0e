const { Client, Databases, Users, ID } = require('node-appwrite');
require('dotenv').config();

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const users = new Users(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

console.log('Environment check:');
console.log('- Endpoint:', process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT);
console.log('- Project ID:', process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID);
console.log('- Database ID:', DATABASE_ID);
console.log('- API Key:', process.env.APPWRITE_API_KEY ? 'Set' : 'Missing');
console.log('');

async function setupDatabase() {
  try {
    console.log('🚀 Setting up AfricSource database...');
    
    // Create admin user
    console.log('👤 Creating admin user...');
    
    try {
      const adminUser = await users.create(
        ID.unique(),
        '<EMAIL>',
        undefined, // phone
        'admin123', // password
        'Admin User' // name
      );
      
      console.log('✅ Admin user created:', adminUser.$id);
      
      // Set user role to ADMIN by creating a user document in the users collection
      await databases.createDocument(
        DATABASE_ID,
        'users',
        adminUser.$id,
        {
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'ADMIN'
        }
      );
      
      console.log('✅ Admin user role set');
      
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️  Admin user already exists');
      } else {
        console.error('❌ Error creating admin user:', error);
      }
    }
    
    // Create sample categories
    console.log('📂 Creating sample categories...');
    
    const categories = [
      { name: 'Grains & Seeds', description: 'Cereals, seeds, and grain products' },
      { name: 'Nuts & Kernels', description: 'Various nuts and kernels' },
      { name: 'Oils & Fats', description: 'Vegetable oils and natural fats' },
      { name: 'Beans & Legumes', description: 'Beans, peas, and other legumes' },
      { name: 'Spices & Herbs', description: 'Spices, herbs, and seasonings' },
      { name: 'Fruits & Vegetables', description: 'Fresh and dried fruits and vegetables' }
    ];
    
    for (const category of categories) {
      try {
        const doc = await databases.createDocument(
          DATABASE_ID,
          'categories',
          ID.unique(),
          category
        );
        console.log(`✅ Created category: ${category.name} (${doc.$id})`);
      } catch (error) {
        if (error.code === 409) {
          console.log(`ℹ️  Category ${category.name} already exists`);
        } else {
          console.error(`❌ Error creating category ${category.name}:`, error);
        }
      }
    }
    
    // Create sample tags
    console.log('🏷️  Creating sample tags...');
    
    const tags = [
      'Organic',
      'Fair Trade',
      'Non-GMO',
      'Sustainable',
      'Premium',
      'Bulk',
      'Export Quality'
    ];
    
    for (const tagName of tags) {
      try {
        const doc = await databases.createDocument(
          DATABASE_ID,
          'tags',
          ID.unique(),
          { name: tagName }
        );
        console.log(`✅ Created tag: ${tagName} (${doc.$id})`);
      } catch (error) {
        if (error.code === 409) {
          console.log(`ℹ️  Tag ${tagName} already exists`);
        } else {
          console.error(`❌ Error creating tag ${tagName}:`, error);
        }
      }
    }
    
    // Create a sample contact submission
    console.log('📧 Creating sample contact submission...');
    
    try {
      const contact = await databases.createDocument(
        DATABASE_ID,
        'contact_submissions',
        ID.unique(),
        {
          name: 'Test User',
          email: '<EMAIL>',
          subject: 'Welcome to AfricSource',
          message: 'This is a sample contact message to test the contact system.',
          status: 'UNREAD'
        }
      );
      console.log(`✅ Created sample contact: ${contact.$id}`);
    } catch (error) {
      console.error('❌ Error creating sample contact:', error);
    }
    
    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('📋 Admin Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('');
    console.log('🌐 You can now start the application with: npm run dev');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
