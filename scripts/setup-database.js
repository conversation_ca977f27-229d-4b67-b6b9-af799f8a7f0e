const { Client, Databases, Users, ID } = require('node-appwrite');
require('dotenv').config();

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const users = new Users(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;

// Collection IDs
const COLLECTIONS = {
  USERS: 'users',
  PRODUCTS: 'products',
  CATEGORIES: 'categories',
  TAGS: 'tags',
  PRODUCT_CATEGORIES: 'product_categories',
  PRODUCT_TAGS: 'product_tags',
  PRODUCT_IMAGES: 'product_images',
  SPECIFICATIONS: 'specifications',
  CONTACT_SUBMISSIONS: 'contact_submissions'
};

console.log('🔧 AfricSource Database Setup');
console.log('Environment check:');
console.log('- Endpoint:', process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT);
console.log('- Project ID:', process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID);
console.log('- Database ID:', DATABASE_ID);
console.log('- API Key:', process.env.APPWRITE_API_KEY ? 'Set' : 'Missing');
console.log('');

async function setupDatabase() {
  try {
    console.log('🚀 Setting up AfricSource database...');

    // Create admin user
    console.log('👤 Creating admin user...');

    try {
      const adminUser = await users.create(
        ID.unique(),
        '<EMAIL>',
        undefined, // phone
        'admin123', // password
        'Admin User' // name
      );

      console.log('✅ Admin user created:', adminUser.$id);

      // Set user role to ADMIN by creating a user document in the users collection
      await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.USERS,
        adminUser.$id,
        {
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'ADMIN'
        }
      );

      console.log('✅ Admin user role set');

    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️  Admin user already exists');
      } else {
        console.error('❌ Error creating admin user:', error);
        console.log('⚠️  Make sure the "users" collection exists in Appwrite');
      }
    }
    
    // Create sample categories
    console.log('📂 Creating sample categories...');

    const categories = [
      { name: 'Grains & Seeds', description: 'Cereals, seeds, and grain products' },
      { name: 'Nuts & Kernels', description: 'Various nuts and kernels' },
      { name: 'Oils & Fats', description: 'Vegetable oils and natural fats' },
      { name: 'Beans & Legumes', description: 'Beans, peas, and other legumes' },
      { name: 'Spices & Herbs', description: 'Spices, herbs, and seasonings' },
      { name: 'Fruits & Vegetables', description: 'Fresh and dried fruits and vegetables' }
    ];

    const createdCategories = [];
    for (const category of categories) {
      try {
        const doc = await databases.createDocument(
          DATABASE_ID,
          COLLECTIONS.CATEGORIES,
          ID.unique(),
          category
        );
        console.log(`✅ Created category: ${category.name} (${doc.$id})`);
        createdCategories.push(doc);
      } catch (error) {
        if (error.code === 409) {
          console.log(`ℹ️  Category ${category.name} already exists`);
        } else {
          console.error(`❌ Error creating category ${category.name}:`, error);
        }
      }
    }
    
    // Create sample tags
    console.log('🏷️  Creating sample tags...');

    const tags = [
      'Organic',
      'Fair Trade',
      'Non-GMO',
      'Sustainable',
      'Premium',
      'Bulk',
      'Export Quality'
    ];

    const createdTags = [];
    for (const tagName of tags) {
      try {
        const doc = await databases.createDocument(
          DATABASE_ID,
          COLLECTIONS.TAGS,
          ID.unique(),
          { name: tagName }
        );
        console.log(`✅ Created tag: ${tagName} (${doc.$id})`);
        createdTags.push(doc);
      } catch (error) {
        if (error.code === 409) {
          console.log(`ℹ️  Tag ${tagName} already exists`);
        } else {
          console.error(`❌ Error creating tag ${tagName}:`, error);
        }
      }
    }
    
    // Create sample products with enhanced image schema
    console.log('📦 Creating sample products...');

    if (createdCategories.length > 0 && createdTags.length > 0) {
      const sampleProducts = [
        {
          name: 'Premium Sesame Seeds',
          slug: 'premium-sesame-seeds',
          description: 'High-quality sesame seeds sourced from the finest farms in Nigeria. Rich in nutrients and perfect for various culinary applications.',
          details: 'Our premium sesame seeds are carefully selected from organic farms in Northern Nigeria. These seeds are known for their exceptional quality, rich flavor, and high oil content. Perfect for tahini production, baking, and cooking. Each batch is thoroughly cleaned and tested for purity.',
          price: '$2,500 per metric ton',
          stock: 'Available',
          images: [
            {
              url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
              alt: 'Premium sesame seeds in bulk',
              isFeatured: true,
              isMain: true,
              isPrimary: true,
              order: 0
            },
            {
              url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600',
              alt: 'Close-up of sesame seeds',
              isFeatured: false,
              isMain: false,
              isPrimary: false,
              order: 1
            }
          ],
          specifications: [
            { key: 'Origin', value: 'Nigeria' },
            { key: 'Purity', value: '99.5%' },
            { key: 'Moisture Content', value: 'Max 6%' },
            { key: 'Oil Content', value: '48-52%' },
            { key: 'Packaging', value: '25kg, 50kg bags' }
          ],
          categoryIds: [createdCategories[0].$id], // Grains & Seeds
          tagIds: [createdTags[0].$id, createdTags[3].$id] // Organic, Sustainable
        },
        {
          name: 'Organic Cashew Nuts',
          slug: 'organic-cashew-nuts',
          description: 'Premium organic cashew nuts from West Africa. Carefully processed to maintain natural flavor and nutritional value.',
          details: 'Our organic cashew nuts are sourced from certified organic farms across West Africa. These cashews are processed using traditional methods to preserve their natural taste and nutritional benefits. Available in various grades and sizes to meet different market requirements.',
          price: '$8,500 per metric ton',
          stock: 'Available',
          images: [
            {
              url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=800',
              alt: 'Organic cashew nuts',
              isFeatured: true,
              isMain: true,
              isPrimary: true,
              order: 0
            }
          ],
          specifications: [
            { key: 'Origin', value: 'West Africa' },
            { key: 'Grade', value: 'W240, W320, W450' },
            { key: 'Moisture Content', value: 'Max 5%' },
            { key: 'Broken Nuts', value: 'Max 5%' },
            { key: 'Certification', value: 'Organic, Fair Trade' }
          ],
          categoryIds: [createdCategories[1].$id], // Nuts & Kernels
          tagIds: [createdTags[0].$id, createdTags[1].$id, createdTags[4].$id] // Organic, Fair Trade, Premium
        }
      ];

      for (const productData of sampleProducts) {
        try {
          // Create the main product
          const product = await databases.createDocument(
            DATABASE_ID,
            COLLECTIONS.PRODUCTS,
            ID.unique(),
            {
              name: productData.name,
              slug: productData.slug,
              description: productData.description,
              details: productData.details,
              price: productData.price,
              stock: productData.stock,
              createdById: 'admin' // This would be the admin user ID in real scenario
            }
          );

          console.log(`✅ Created product: ${product.name} (${product.$id})`);

          // Create product images with enhanced schema
          for (const imageData of productData.images) {
            try {
              await databases.createDocument(
                DATABASE_ID,
                COLLECTIONS.PRODUCT_IMAGES,
                ID.unique(),
                {
                  ...imageData,
                  productId: product.$id
                }
              );
              console.log(`  ✅ Added image: ${imageData.alt}`);
            } catch (error) {
              console.error(`  ❌ Error creating image:`, error);
            }
          }

          // Create specifications
          for (const spec of productData.specifications) {
            try {
              await databases.createDocument(
                DATABASE_ID,
                COLLECTIONS.SPECIFICATIONS,
                ID.unique(),
                {
                  ...spec,
                  productId: product.$id
                }
              );
              console.log(`  ✅ Added specification: ${spec.key}`);
            } catch (error) {
              console.error(`  ❌ Error creating specification:`, error);
            }
          }

          // Create category relationships
          for (const categoryId of productData.categoryIds) {
            try {
              await databases.createDocument(
                DATABASE_ID,
                COLLECTIONS.PRODUCT_CATEGORIES,
                ID.unique(),
                {
                  productId: product.$id,
                  categoryId: categoryId
                }
              );
              console.log(`  ✅ Added category relationship`);
            } catch (error) {
              console.error(`  ❌ Error creating category relationship:`, error);
            }
          }

          // Create tag relationships
          for (const tagId of productData.tagIds) {
            try {
              await databases.createDocument(
                DATABASE_ID,
                COLLECTIONS.PRODUCT_TAGS,
                ID.unique(),
                {
                  productId: product.$id,
                  tagId: tagId
                }
              );
              console.log(`  ✅ Added tag relationship`);
            } catch (error) {
              console.error(`  ❌ Error creating tag relationship:`, error);
            }
          }

        } catch (error) {
          console.error(`❌ Error creating product ${productData.name}:`, error);
        }
      }
    } else {
      console.log('⚠️  Skipping product creation - no categories or tags available');
    }

    // Create a sample contact submission
    console.log('📧 Creating sample contact submission...');

    try {
      const contact = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CONTACT_SUBMISSIONS,
        ID.unique(),
        {
          name: 'John Smith',
          email: '<EMAIL>',
          company: 'Global Trading Co.',
          subject: 'Inquiry about Sesame Seeds',
          message: 'Hello, I am interested in purchasing premium sesame seeds for our European market. Could you please provide more details about pricing, minimum order quantities, and shipping terms?',
          status: 'UNREAD'
        }
      );
      console.log(`✅ Created sample contact: ${contact.$id}`);
    } catch (error) {
      console.error('❌ Error creating sample contact:', error);
    }
    
    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('📋 Admin Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('');
    console.log('🌐 You can now start the application with: npm run dev');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
