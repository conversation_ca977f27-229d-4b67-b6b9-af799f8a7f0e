#!/usr/bin/env ts-node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface VerificationResult {
  totalImages: number;
  migratedImages: number;
  localImages: number;
  brokenLinks: number;
  details: {
    id: string;
    url: string;
    status: 'migrated' | 'local' | 'broken';
    accessible?: boolean;
  }[];
}

class ImageMigrationVerifier {
  async verify(): Promise<VerificationResult> {
    console.log('🔍 Verifying image migration status...\n');

    const result: VerificationResult = {
      totalImages: 0,
      migratedImages: 0,
      localImages: 0,
      brokenLinks: 0,
      details: []
    };

    try {
      // Get all product images
      const productImages = await prisma.productImage.findMany({
        orderBy: { createdAt: 'desc' }
      });

      result.totalImages = productImages.length;
      console.log(`📊 Found ${productImages.length} product images in database\n`);

      // Check each image
      for (const image of productImages) {
        const detail: {
          id: string;
          url: string;
          status: 'migrated' | 'local' | 'broken';
          accessible?: boolean;
        } = {
          id: image.id,
          url: image.url,
          status: 'local',
          accessible: false
        };

        // Determine if image is migrated or local
        if (image.url.includes(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')) {
          detail.status = 'migrated';
          result.migratedImages++;
          
          // Test if Appwrite file is accessible
          const fileId = this.extractFileIdFromUrl(image.url);
          if (fileId) {
            detail.accessible = true;
            console.log(`✅ Migrated: ${image.url.split('/').pop()}`);
          } else {
            detail.status = 'broken';
            result.brokenLinks++;
            console.log(`❌ Broken URL format: ${image.url}`);
          }
        } else if (image.url.startsWith('/uploads/')) {
          detail.status = 'local';
          result.localImages++;
          console.log(`📁 Local file: ${image.url}`);
        } else {
          detail.status = 'broken';
          result.brokenLinks++;
          console.log(`❓ Unknown URL format: ${image.url}`);
        }

        result.details.push(detail);
      }

      // Print summary
      this.printSummary(result);

      return result;
    } catch (error) {
      console.error('💥 Verification failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private extractFileIdFromUrl(url: string): string | null {
    try {
      const match = url.match(/\/files\/([^\/\?]+)/);
      return match ? match[1] : null;
    } catch (error) {
      return null;
    }
  }

  private printSummary(result: VerificationResult): void {
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total images: ${result.totalImages}`);
    console.log(`✅ Migrated to Appwrite: ${result.migratedImages} (${((result.migratedImages / result.totalImages) * 100).toFixed(1)}%)`);
    console.log(`📁 Still local: ${result.localImages} (${((result.localImages / result.totalImages) * 100).toFixed(1)}%)`);
    console.log(`❌ Broken links: ${result.brokenLinks} (${((result.brokenLinks / result.totalImages) * 100).toFixed(1)}%)`);

    if (result.brokenLinks > 0) {
      console.log('\n❌ BROKEN LINKS:');
      result.details
        .filter(d => d.status === 'broken')
        .forEach(d => console.log(`   - ${d.id}: ${d.url}`));
    }

    if (result.localImages > 0) {
      console.log('\n📁 REMAINING LOCAL FILES:');
      result.details
        .filter(d => d.status === 'local')
        .forEach(d => console.log(`   - ${d.url}`));
    }

    // Migration status
    if (result.migratedImages === result.totalImages) {
      console.log('\n🎉 All images have been successfully migrated!');
    } else if (result.migratedImages > 0) {
      console.log('\n⚠️  Migration is partially complete.');
      console.log(`   ${result.localImages} images still need to be migrated.`);
    } else {
      console.log('\n❌ No images have been migrated yet.');
    }
  }

  /**
   * Check Appwrite Storage bucket status
   */
  async checkStorageBucket(): Promise<void> {
    console.log('🪣 Checking Appwrite Storage bucket...\n');
    console.log('ℹ️  Storage bucket check requires Appwrite SDK - skipping for now');
    console.log('   You can verify storage manually in the Appwrite Console');
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const verifier = new ImageMigrationVerifier();

  try {
    switch (command) {
      case 'verify':
        await verifier.verify();
        break;
      case 'storage':
        await verifier.checkStorageBucket();
        break;
      case 'full':
        await verifier.checkStorageBucket();
        console.log('\n');
        await verifier.verify();
        break;
      default:
        console.log('Usage:');
        console.log('  npm run verify:images verify  - Verify migration status');
        console.log('  npm run verify:images storage - Check Appwrite storage');
        console.log('  npm run verify:images full    - Full verification');
        process.exit(1);
    }
  } catch (error) {
    console.error('Verification failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { ImageMigrationVerifier };
