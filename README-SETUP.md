# AfricSource Application Setup Guide

## 🎯 Current Status

The AfricSource application has been fully developed with enhanced features including:

### ✅ Completed Features

#### **Phase 1: Database Schema & Product System**
- **Enhanced Image Schema**: Updated `ProductImageDocument` interface with `isMain`, `isPrimary`, and `order` fields
- **Improved Product Creation**: Enhanced `createProduct` method with better error handling and relationship management
- **Database Schema Documentation**: Complete schema documentation in `docs/database-schema.md`
- **Setup Scripts**: Comprehensive database setup script in `scripts/setup-database.js`

#### **Phase 2: SEO & Accessibility**
- **Comprehensive SEO Library**: Created `src/lib/seo.ts` with metadata generation utilities
- **Structured Data**: Organization and product structured data implementation
- **Sitemap Generation**: Dynamic sitemap at `/sitemap.xml` with product and category pages
- **Robots.txt**: Proper robots.txt configuration for search engines
- **Enhanced Metadata**: Updated all page metadata with proper SEO optimization
- **Accessibility Library**: Created `src/lib/accessibility.ts` with WCAG 2.1 AA compliance utilities
- **Skip Links**: Implemented skip links for keyboard navigation
- **ARIA Landmarks**: Added proper semantic HTML and ARIA attributes
- **Accessible Modal**: Created reusable accessible modal component
- **Focus Management**: Proper focus trapping and restoration

#### **Additional Enhancements**
- **Dashboard Contacts Page**: Complete contact management system with status updates
- **Enhanced Navigation**: Added Messages menu item to dashboard sidebar
- **Multi-Image Gallery**: Product detail pages with image gallery functionality
- **Improved Error Handling**: Better error handling throughout the application
- **Description Truncation**: Product list UI improvements

## 🚀 Next Steps

### **Step 1: Create Appwrite Database Collections**

The application uses database ID: `687e7c56003ba09479b1`

**Required Collections:**

1. **users** - User authentication and roles
2. **products** - Main product information
3. **categories** - Product categories
4. **tags** - Product tags
5. **product_categories** - Product-category relationships
6. **product_tags** - Product-tag relationships
7. **product_images** - Enhanced image metadata (with isMain, isPrimary, order fields)
8. **specifications** - Product specifications
9. **contact_submissions** - Contact form submissions

**Detailed Schema:** See `docs/database-schema.md` for complete field definitions, types, and permissions.

### **Step 2: Run Database Setup**

After creating collections in Appwrite Console:

```bash
# Install dependencies if not already done
npm install

# Run the database setup script
node scripts/setup-database.js
```

This will create:
- Admin user (<EMAIL> / admin123)
- Sample categories and tags
- Sample products with enhanced image metadata
- Sample contact submissions

### **Step 3: Verify Setup**

```bash
# Check database status
curl -X GET http://localhost:3000/api/setup

# Test admin login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 🔧 Key Features

### **Enhanced Image Management**
- **Multiple Images per Product**: Support for multiple product images
- **Image Metadata**: `isMain`, `isPrimary`, `order` fields for better organization
- **Gallery Display**: Interactive image gallery on product detail pages
- **Responsive Design**: Optimized for all device sizes

### **SEO Optimization**
- **Dynamic Metadata**: Automatic generation of page titles, descriptions, and Open Graph tags
- **Structured Data**: JSON-LD structured data for products and organization
- **Sitemap**: Auto-generated XML sitemap with product and category pages
- **Robots.txt**: Proper search engine directives

### **Accessibility (WCAG 2.1 AA)**
- **Skip Links**: Keyboard navigation shortcuts
- **ARIA Landmarks**: Proper semantic HTML structure
- **Focus Management**: Keyboard navigation and focus trapping
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Color Contrast**: Utilities for checking WCAG compliance

### **Contact Management**
- **Admin Dashboard**: Complete contact message management
- **Status Tracking**: UNREAD → READ → REPLIED → CLOSED workflow
- **Pagination**: Efficient handling of large contact lists
- **Email Integration**: Direct email reply functionality

## 📁 Project Structure

```
src/
├── app/
│   ├── (pages)/           # Public pages with SEO metadata
│   ├── dashboard/         # Admin dashboard
│   ├── api/              # API endpoints
│   └── sitemap.ts        # Dynamic sitemap generation
├── components/
│   ├── accessibility/    # Accessible UI components
│   ├── layouts/          # Layout components
│   ├── molecules/        # Form components
│   └── organisms/        # Complex UI components
├── lib/
│   ├── seo.ts           # SEO utilities and metadata generation
│   ├── accessibility.ts # Accessibility utilities
│   └── appwrite-server.ts # Enhanced database operations
├── types/
│   └── appwrite.ts      # Enhanced TypeScript interfaces
└── docs/
    └── database-schema.md # Complete database documentation
```

## 🌐 API Endpoints

### **Public Endpoints**
- `GET /api/products` - List products with search and filtering
- `GET /api/products/[id]` - Get product details
- `GET /api/categories` - List categories
- `GET /api/tags` - List tags
- `POST /api/contact` - Submit contact form
- `GET /sitemap.xml` - Dynamic sitemap
- `GET /robots.txt` - Search engine directives

### **Admin Endpoints**
- `POST /api/auth/login` - Admin authentication
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/update-profile` - Update user profile
- `PUT /api/auth/change-password` - Change password
- `GET /api/contact` - List contact submissions (admin only)
- `PUT /api/contact/[id]` - Update contact status
- `POST /api/upload` - Upload images
- `DELETE /api/images/[id]` - Delete images

### **Development Endpoints**
- `GET /api/setup` - Check database collection status
- `POST /api/setup` - Get setup instructions

## 🎨 Design Features

- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Modern UI**: Clean, professional design with African-inspired color palette
- **Smooth Animations**: AOS (Animate On Scroll) integration
- **Glassmorphism**: Modern glass-effect design elements
- **Accessibility**: WCAG 2.1 AA compliant design patterns

## 🔒 Security Features

- **Admin Authentication**: Secure admin-only access to dashboard
- **Role-based Access**: User and admin role separation
- **Input Validation**: Comprehensive form validation
- **CSRF Protection**: Built-in Next.js security features
- **Secure File Upload**: Validated image uploads with size limits

## 📱 Mobile Optimization

- **Responsive Images**: Optimized image loading and display
- **Touch-friendly UI**: Large touch targets and smooth interactions
- **Mobile Navigation**: Collapsible mobile menu with animations
- **Performance**: Optimized for mobile networks and devices

## 🚀 Deployment Ready

The application is production-ready with:
- Environment variable configuration
- Error boundaries and logging
- SEO optimization
- Performance optimization
- Security best practices

## 📞 Support

For questions or issues:
1. Check the database schema documentation
2. Verify all collections are created in Appwrite
3. Run the setup verification endpoints
4. Check the browser console for any errors

The application is now fully functional and ready for production use once the Appwrite database collections are created and populated with the setup script.
